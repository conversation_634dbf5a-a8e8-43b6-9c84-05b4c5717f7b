#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Web界面应用 - 豆瓣电影数据处理工具
"""
import os
import json
import logging
import psutil
import time
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, send_from_directory
from werkzeug.utils import secure_filename
from pathlib import Path
import threading
import asyncio
from datetime import datetime, timedelta
from infini_processor_v2 import InfiniDirectorProcessor, load_config

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'

# 配置
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'csv', 'pkl'}
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024  # 500MB max file size

# 创建上传目录
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs('logs', exist_ok=True)

# 全局变量
processing_tasks = {}
active_processors = {}  # 存储活跃的处理器对象
system_stats = {
    'start_time': datetime.now(),
    'total_requests': 0,
    'total_processed_items': 0,
    'api_calls': 0,
    'errors': 0
}
TASKS_FILE = "processing_tasks.json"
STATS_FILE = "system_stats.json"

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/web_app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ErrorHandler:
    """错误处理和记录类"""
    
    @staticmethod
    def log_error(error_type, error_msg, context=None):
        """记录错误信息"""
        global system_stats
        system_stats['errors'] += 1
        save_stats()
        
        error_info = {
            'timestamp': datetime.now().isoformat(),
            'type': error_type,
            'message': str(error_msg),
            'context': context or {}
        }
        
        # 记录到日志文件
        logger.error(f"[{error_type}] {error_msg} | Context: {context}")
        
        # 保存到错误日志文件
        try:
            error_log_file = 'logs/errors.json'
            errors = []
            
            if os.path.exists(error_log_file):
                with open(error_log_file, 'r', encoding='utf-8') as f:
                    errors = json.load(f)
            
            errors.append(error_info)
            
            # 只保留最近1000条错误记录
            if len(errors) > 1000:
                errors = errors[-1000:]
            
            with open(error_log_file, 'w', encoding='utf-8') as f:
                json.dump(errors, f, indent=2, ensure_ascii=False, default=str)
        except Exception as e:
            logger.error(f"保存错误日志失败: {str(e)}")
    
    @staticmethod
    def handle_api_error(func):
        """API错误处理装饰器"""
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                ErrorHandler.log_error('API_ERROR', str(e), {'function': func.__name__})
                return jsonify({'success': False, 'message': f'服务器内部错误: {str(e)}'})
        return wrapper

def load_stats():
    """从文件加载系统统计"""
    global system_stats
    try:
        if os.path.exists(STATS_FILE):
            with open(STATS_FILE, 'r', encoding='utf-8') as f:
                loaded_stats = json.load(f)
            # 转换时间字符串回datetime对象
            if 'start_time' in loaded_stats:
                loaded_stats['start_time'] = datetime.fromisoformat(loaded_stats['start_time'])
            system_stats.update(loaded_stats)
    except Exception as e:
        logger.error(f"加载系统统计失败: {str(e)}")

def save_stats():
    """保存系统统计到文件"""
    try:
        stats_to_save = system_stats.copy()
        # 转换datetime为字符串
        if 'start_time' in stats_to_save:
            stats_to_save['start_time'] = stats_to_save['start_time'].isoformat()
        
        with open(STATS_FILE, 'w', encoding='utf-8') as f:
            json.dump(stats_to_save, f, indent=2, ensure_ascii=False, default=str)
    except Exception as e:
        logger.error(f"保存系统统计失败: {str(e)}")

def load_tasks():
    """从文件加载任务列表"""
    global processing_tasks
    try:
        if os.path.exists(TASKS_FILE):
            with open(TASKS_FILE, 'r', encoding='utf-8') as f:
                loaded_tasks = json.load(f)
            processing_tasks.clear()
            processing_tasks.update(loaded_tasks)
        else:
            processing_tasks.clear()
    except Exception as e:
        logger.error(f"加载任务列表失败: {str(e)}")
        processing_tasks.clear()

def save_tasks():
    """保存任务列表到文件（不包含处理器对象）"""
    try:
        # 创建一个不包含处理器对象的副本
        tasks_to_save = {}
        for task_id, task in processing_tasks.items():
            task_copy = task.copy()
            # 移除处理器对象，因为它无法序列化
            if 'processor' in task_copy:
                del task_copy['processor']
            tasks_to_save[task_id] = task_copy

        with open(TASKS_FILE, 'w', encoding='utf-8') as f:
            json.dump(tasks_to_save, f, indent=2, ensure_ascii=False)
    except Exception as e:
        logger.error(f"保存任务列表失败: {str(e)}")

# 初始化时加载任务和统计
load_tasks()
load_stats()

@app.before_request
def before_request():
    """请求前处理"""
    global system_stats
    system_stats['total_requests'] += 1
    save_stats()

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/system_stats')
def get_system_stats():
    """获取系统统计信息"""
    try:
        # 获取系统信息
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # 计算运行时间
        uptime = datetime.now() - system_stats['start_time']
        uptime_seconds = int(uptime.total_seconds())
        
        # 统计任务信息
        task_stats = {
            'total_tasks': len(processing_tasks),
            'running_tasks': len([t for t in processing_tasks.values() if t.get('status') == 'running']),
            'completed_tasks': len([t for t in processing_tasks.values() if t.get('status') == 'completed']),
            'error_tasks': len([t for t in processing_tasks.values() if t.get('status') == 'error']),
            'paused_tasks': len([t for t in processing_tasks.values() if t.get('status') == 'paused'])
        }
        
        # 计算处理统计
        total_processed = sum([
            t.get('progress_data', {}).get('processed_count', 0) 
            for t in processing_tasks.values() 
            if t.get('progress_data')
        ])
        
        total_items = sum([
            t.get('progress_data', {}).get('total_count', 0) 
            for t in processing_tasks.values() 
            if t.get('progress_data')
        ])
        
        success_rate = (total_processed / total_items * 100) if total_items > 0 else 0
        
        return jsonify({
            'success': True,
            'system': {
                'uptime_seconds': uptime_seconds,
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_used_gb': round(memory.used / 1024**3, 2),
                'memory_total_gb': round(memory.total / 1024**3, 2),
                'disk_percent': disk.percent,
                'disk_free_gb': round(disk.free / 1024**3, 2),
                'disk_total_gb': round(disk.total / 1024**3, 2)
            },
            'stats': {
                'total_requests': system_stats.get('total_requests', 0),
                'total_processed_items': total_processed,
                'api_calls': system_stats.get('api_calls', 0),
                'errors': system_stats.get('errors', 0),
                'success_rate': round(success_rate, 2)
            },
            'tasks': task_stats,
            'performance': {
                'avg_processing_speed': calculate_avg_speed(),
                'current_load': get_current_load()
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取系统统计失败: {str(e)}'})

@app.route('/api/performance_metrics')
def get_performance_metrics():
    """获取性能指标"""
    try:
        # 计算最近24小时的任务完成情况
        now = datetime.now()
        last_24h = now - timedelta(hours=24)
        
        recent_tasks = []
        for task in processing_tasks.values():
            if task.get('start_time'):
                start_time = datetime.fromisoformat(task['start_time'])
                if start_time >= last_24h:
                    recent_tasks.append(task)
        
        # 按小时分组统计
        hourly_stats = {}
        for i in range(24):
            hour = (now - timedelta(hours=i)).replace(minute=0, second=0, microsecond=0)
            hourly_stats[hour.strftime('%H:00')] = {
                'completed': 0,
                'errors': 0,
                'total_processed': 0
            }
        
        for task in recent_tasks:
            task_time = datetime.fromisoformat(task['start_time'])
            hour_key = task_time.strftime('%H:00')
            
            if hour_key in hourly_stats:
                if task.get('status') == 'completed':
                    hourly_stats[hour_key]['completed'] += 1
                elif task.get('status') == 'error':
                    hourly_stats[hour_key]['errors'] += 1
                
                if task.get('progress_data', {}).get('processed_count'):
                    hourly_stats[hour_key]['total_processed'] += task['progress_data']['processed_count']
        
        return jsonify({
            'success': True,
            'hourly_stats': hourly_stats,
            'recent_tasks_count': len(recent_tasks),
            'avg_completion_time': calculate_avg_completion_time(),
            'error_rate': calculate_error_rate()
        })
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取性能指标失败: {str(e)}'})

def calculate_avg_speed():
    """计算平均处理速度"""
    try:
        completed_tasks = [t for t in processing_tasks.values() if t.get('status') == 'completed']
        if not completed_tasks:
            return 0
        
        total_items = 0
        total_time = 0
        
        for task in completed_tasks:
            if task.get('start_time') and task.get('end_time'):
                start = datetime.fromisoformat(task['start_time'])
                end = datetime.fromisoformat(task['end_time'])
                duration = (end - start).total_seconds()
                
                if duration > 0 and task.get('progress_data', {}).get('processed_count'):
                    items = task['progress_data']['processed_count']
                    total_items += items
                    total_time += duration
        
        return round(total_items / total_time, 2) if total_time > 0 else 0
    except:
        return 0

def get_current_load():
    """获取当前系统负载"""
    try:
        running_tasks = len([t for t in processing_tasks.values() if t.get('status') == 'running'])
        cpu_percent = psutil.cpu_percent()
        memory_percent = psutil.virtual_memory().percent
        
        # 简单的负载评级
        if running_tasks > 3 or cpu_percent > 80 or memory_percent > 85:
            return 'high'
        elif running_tasks > 1 or cpu_percent > 50 or memory_percent > 60:
            return 'medium'
        else:
            return 'low'
    except:
        return 'unknown'

def calculate_avg_completion_time():
    """计算平均完成时间"""
    try:
        completed_tasks = [t for t in processing_tasks.values() if t.get('status') == 'completed']
        if not completed_tasks:
            return 0
        
        total_duration = 0
        count = 0
        
        for task in completed_tasks:
            if task.get('start_time') and task.get('end_time'):
                start = datetime.fromisoformat(task['start_time'])
                end = datetime.fromisoformat(task['end_time'])
                duration = (end - start).total_seconds()
                total_duration += duration
                count += 1
        
        return round(total_duration / count / 60, 2) if count > 0 else 0  # 返回分钟
    except:
        return 0

def calculate_error_rate():
    """计算错误率"""
    try:
        total_tasks = len(processing_tasks)
        if total_tasks == 0:
            return 0
        
        error_tasks = len([t for t in processing_tasks.values() if t.get('status') == 'error'])
        return round(error_tasks / total_tasks * 100, 2)
    except:
        return 0

@app.route('/api/logs')
def get_logs():
    """获取系统日志"""
    try:
        log_type = request.args.get('type', 'all')
        limit = int(request.args.get('limit', 100))
        
        logs = []
        
        # 获取错误日志
        if log_type in ['all', 'errors']:
            error_log_file = 'logs/errors.json'
            if os.path.exists(error_log_file):
                with open(error_log_file, 'r', encoding='utf-8') as f:
                    error_logs = json.load(f)
                    for log in error_logs[-limit:]:
                        log['level'] = 'ERROR'
                        logs.append(log)
        
        # 获取应用日志
        if log_type in ['all', 'app']:
            app_log_file = 'logs/web_app.log'
            if os.path.exists(app_log_file):
                try:
                    with open(app_log_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        for line in lines[-limit:]:
                            if line.strip():
                                parts = line.split(' - ', 3)
                                if len(parts) >= 4:
                                    logs.append({
                                        'timestamp': parts[0],
                                        'level': parts[2],
                                        'message': parts[3].strip(),
                                        'type': 'APP_LOG'
                                    })
                except Exception as e:
                    logger.error(f"读取应用日志失败: {str(e)}")
        
        # 按时间排序
        logs.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
        
        return jsonify({
            'success': True,
            'logs': logs[:limit],
            'total_count': len(logs)
        })
    except Exception as e:
        ErrorHandler.log_error('LOG_API_ERROR', str(e))
        return jsonify({'success': False, 'message': f'获取日志失败: {str(e)}'})

@app.route('/api/clear_logs', methods=['POST'])
def clear_logs():
    """清理日志"""
    try:
        log_type = request.json.get('type', 'errors')
        
        if log_type == 'errors':
            error_log_file = 'logs/errors.json'
            if os.path.exists(error_log_file):
                # 备份并清空错误日志
                backup_file = f"logs/errors_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                os.rename(error_log_file, backup_file)
                
            return jsonify({'success': True, 'message': '错误日志已清理'})
        elif log_type == 'app':
            app_log_file = 'logs/web_app.log'
            if os.path.exists(app_log_file):
                # 备份并清空应用日志
                backup_file = f"logs/web_app_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
                os.rename(app_log_file, backup_file)
                
            return jsonify({'success': True, 'message': '应用日志已清理'})
        else:
            return jsonify({'success': False, 'message': '无效的日志类型'})
    except Exception as e:
        ErrorHandler.log_error('LOG_CLEAR_ERROR', str(e))
        return jsonify({'success': False, 'message': f'清理日志失败: {str(e)}'})

@app.route('/logs')
def logs_page():
    """日志管理页面"""
    return render_template('logs.html')

@app.route('/analytics')
def analytics_page():
    """数据分析页面"""
    return render_template('analytics.html')

@app.route('/api/dataset_info')
def dataset_info():
    """获取数据集信息"""
    try:
        dataset_path = Path('dataset/all_data.pkl')
        if dataset_path.exists():
            import pandas as pd
            df = pd.read_pickle(dataset_path)
            
            # 获取基本统计信息
            # 处理sample_data中的NaN值
            sample_df = df.head(3).fillna('')  # 将NaN替换为空字符串
            sample_data = sample_df.to_dict('records')

            info = {
                'exists': True,
                'size_mb': round(dataset_path.stat().st_size / 1024 / 1024, 2),
                'total_records': int(len(df)),
                'columns': list(df.columns),
                'sample_data': sample_data
            }
            
            # 统计不同角色字段的情况
            field_stats = {}
            for field in ['daoyan', 'bianjv', 'zhuyan']:
                if field in df.columns:
                    non_null_count = df[field].notna().sum()
                    field_stats[field] = {
                        'total': int(non_null_count),
                        'sample_records': [str(x) for x in df[field].dropna().head(3).tolist()]
                    }
            
            info['field_stats'] = field_stats
            
            return jsonify({'success': True, 'data': info})
        else:
            return jsonify({'success': False, 'message': '数据集文件不存在'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'读取数据集失败: {str(e)}'})

@app.route('/api/role_prompts')
def role_prompts():
    """获取不同角色的提示词配置"""
    prompts = {
        'director': {
            'system': '你是一个专业的数据提取助手，请严格遵循输出格式。你需要搜索并提取电影导演的详细信息。',
            'user_template': '''请搜索并提取{person_name}（电影"{movie_title}"的导演）的详细信息。

请提供以下信息，并以JSON格式返回，使用以下字段名：

1. chinese_name: 中文名（如果没有官方中文名，则使用音译）
2. english_name: 英文名（如果没有官方英文名，则使用音译/拼音）
3. birth_date: 出生日期（YYYY-MM-DD格式，如果只知道年份则只返回YYYY）
4. education_institution: 教育机构（英文）
5. has_university_education: 是否接受过大学教育（布尔值）
6. profession: 主要职业（英文）
7. birth_country: 出生国家（使用ISO三字母国家代码，如CHN、USA）
8. birth_city: 出生城市（英文）
9. gender: 性别（"male"、"female"或null）
10. nationality: 国籍（使用ISO三字母国家代码）

请只返回JSON格式的数据。对于未知信息使用null。如果可能，请简要说明为什么某些信息无法获取。'''
        },
        'screenwriter': {
            'system': '你是一个专业的数据提取助手，请严格遵循输出格式。你需要搜索并提取电影编剧的详细信息。',
            'user_template': '''请搜索并提取{person_name}（电影"{movie_title}"的编剧）的详细信息。

请提供以下信息，并以JSON格式返回，使用以下字段名：

1. chinese_name: 中文名（如果没有官方中文名，则使用音译）
2. english_name: 英文名（如果没有官方英文名，则使用音译/拼音）
3. birth_date: 出生日期（YYYY-MM-DD格式，如果只知道年份则只返回YYYY）
4. education_institution: 教育机构（英文）
5. has_university_education: 是否接受过大学教育（布尔值）
6. profession: 主要职业（英文）
7. birth_country: 出生国家（使用ISO三字母国家代码，如CHN、USA）
8. birth_city: 出生城市（英文）
9. gender: 性别（"male"、"female"或null）
10. nationality: 国籍（使用ISO三字母国家代码）

请只返回JSON格式的数据。对于未知信息使用null。如果可能，请简要说明为什么某些信息无法获取。'''
        },
        'actor': {
            'system': '你是一个专业的数据提取助手，请严格遵循输出格式。你需要搜索并提取电影演员的详细信息。',
            'user_template': '''请搜索并提取{person_name}（电影"{movie_title}"的演员）的详细信息。

请提供以下信息，并以JSON格式返回，使用以下字段名：

1. chinese_name: 中文名（如果没有官方中文名，则使用音译）
2. english_name: 英文名（如果没有官方英文名，则使用音译/拼音）
3. birth_date: 出生日期（YYYY-MM-DD格式，如果只知道年份则只返回YYYY）
4. education_institution: 教育机构（英文）
5. has_university_education: 是否接受过大学教育（布尔值）
6. profession: 主要职业（英文）
7. birth_country: 出生国家（使用ISO三字母国家代码，如CHN、USA）
8. birth_city: 出生城市（英文）
9. gender: 性别（"male"、"female"或null）
10. nationality: 国籍（使用ISO三字母国家代码）

请只返回JSON格式的数据。对于未知信息使用null。如果可能，请简要说明为什么某些信息无法获取。'''
        }
    }
    
    return jsonify({'success': True, 'prompts': prompts})

@app.route('/config')
def config():
    """配置页面"""
    try:
        if os.path.exists('infini_config.json'):
            with open('infini_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
        else:
            config = {}
        return render_template('config.html', config=config)
    except Exception as e:
        flash(f'加载配置失败: {str(e)}', 'error')
        return render_template('config.html', config={})

@app.route('/api/config', methods=['GET', 'POST'])
def api_config():
    """API配置接口"""
    if request.method == 'POST':
        try:
            config_data = request.get_json()
            
            # 保存配置到文件
            with open('infini_config.json', 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=4, ensure_ascii=False)
            
            return jsonify({'success': True, 'message': '配置已保存'})
        except Exception as e:
            return jsonify({'success': False, 'message': f'保存失败: {str(e)}'})
    
    # GET请求
    try:
        if os.path.exists('infini_config.json'):
            with open('infini_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            return jsonify(config)
        else:
            return jsonify({'success': False, 'message': '配置文件不存在'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'加载失败: {str(e)}'})

@app.route('/upload', methods=['GET', 'POST'])
def upload_file():
    """文件上传页面"""
    if request.method == 'POST':
        data_source = request.form.get('data_source', 'upload')
        
        if data_source == 'dataset':
            # 使用dataset中的文件
            flash('已选择使用dataset数据', 'success')
            return redirect(url_for('process', filename='dataset/all_data.pkl'))
        else:
            # 上传文件
            if 'file' not in request.files:
                flash('没有选择文件', 'error')
                return redirect(request.url)
            
            file = request.files['file']
            if file.filename == '':
                flash('没有选择文件', 'error')
                return redirect(request.url)
            
            if file and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                file.save(filepath)
                
                flash(f'文件 {filename} 上传成功', 'success')
                return redirect(url_for('process', filename=filename))
            else:
                flash('文件格式不支持，请上传CSV或PKL文件', 'error')
    
    return render_template('upload.html')

@app.route('/process/<filename>')
def process(filename):
    """处理页面"""
    filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    if not os.path.exists(filepath):
        flash('文件不存在', 'error')
        return redirect(url_for('upload_file'))
    
    return render_template('process.html', filename=filename)

@app.route('/api/start_processing', methods=['POST'])
def start_processing():
    """开始处理"""
    try:
        data = request.get_json()
        filename = data.get('filename')
        data_type = data.get('data_type', 'movie')  # 新增数据类型参数，默认为movie
        field_to_process = data.get('field_to_process', 'actor')
        min_reviews = data.get('min_reviews', 0)
        min_short_reviews = data.get('min_short_reviews', 0)
        concurrency = data.get('concurrency', 30)

        # 验证数据类型
        valid_data_types = ['movie', 'book', 'tv']
        if data_type not in valid_data_types:
            return jsonify({'success': False, 'message': f'无效的数据类型: {data_type}，支持的类型: {", ".join(valid_data_types)}'})

        # 验证字段类型（根据数据类型）
        field_mappings = {
            'movie': ['director', 'screenwriter', 'actor'],
            'book': ['author', 'translator', 'editor'],
            'tv': ['director', 'writer', 'cast']
        }
        valid_fields = field_mappings.get(data_type, field_mappings['movie'])
        if field_to_process not in valid_fields:
            return jsonify({'success': False, 'message': f'数据类型 {data_type} 不支持字段 {field_to_process}，支持的字段: {", ".join(valid_fields)}'})
        
        if not filename:
            return jsonify({'success': False, 'message': '缺少文件名'})
        
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        if not os.path.exists(filepath):
            return jsonify({'success': False, 'message': '文件不存在'})
        
        # 创建任务ID
        task_id = f"task_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 在后台线程中启动处理任务
        def run_processing():
            try:
                config = load_config()
                
                processor = InfiniDirectorProcessor(
                    api_keys=config["api_keys"],
                    data_path=filepath,
                    output_dir=config.get("output_dir", "infini_output"),
                    max_workers=concurrency,  # 使用用户设置的并发数
                    batch_size=config.get("batch_size", 100),
                    field_to_process=field_to_process,
                    data_type=data_type,  # 新增数据类型参数
                    test_mode=config.get("test_mode", False),
                    test_limit=config.get("test_limit", 50),
                    min_reviews=min_reviews,
                    min_short_reviews=min_short_reviews,
                    retry_refused=config.get("retry_refused", True),
                    retry_failed=config.get("retry_failed", True),
                    config=config
                )
                
                processing_tasks[task_id] = {
                    'status': 'running',
                    'start_time': datetime.now().isoformat(),
                    'filename': filename,
                    'field_to_process': field_to_process,
                    'concurrency': concurrency,
                    'progress': 0,
                    'message': '正在初始化...',
                    'id': task_id
                }
                # 将处理器对象保存在内存中
                active_processors[task_id] = processor
                save_tasks()  # 保存任务列表
                
                # 启动进度更新线程
                def update_progress():
                    while processing_tasks.get(task_id, {}).get('status') == 'running':
                        try:
                            if hasattr(processor, 'processed_count') and hasattr(processor, 'total_count'):
                                processed = getattr(processor, 'processed_count', 0)
                                total = getattr(processor, 'total_count', 0)
                                current_item = getattr(processor, 'current_item', '')
                                
                                if total > 0:
                                    progress = (processed / total) * 100
                                else:
                                    progress = 0
                                
                                processing_tasks[task_id]['progress'] = progress
                                processing_tasks[task_id]['message'] = f'正在处理: {current_item}' if current_item else f'已处理 {processed}/{total} 项'
                                save_tasks()
                        except:
                            pass
                        
                        import time
                        time.sleep(2)  # 每2秒更新一次
                
                progress_thread = threading.Thread(target=update_progress)
                progress_thread.daemon = True
                progress_thread.start()
                
                # 运行异步处理
                asyncio.run(processor.process_data())
                
                processing_tasks[task_id]['status'] = 'completed'
                processing_tasks[task_id]['progress'] = 100
                processing_tasks[task_id]['message'] = '处理完成'
                processing_tasks[task_id]['end_time'] = datetime.now().isoformat()
                # 清理处理器对象
                if task_id in active_processors:
                    del active_processors[task_id]
                save_tasks()  # 保存任务列表
                
            except Exception as e:
                processing_tasks[task_id]['status'] = 'error'
                processing_tasks[task_id]['error'] = str(e)
                processing_tasks[task_id]['message'] = f'处理出错: {str(e)}'
                processing_tasks[task_id]['end_time'] = datetime.now().isoformat()
                # 清理处理器对象
                if task_id in active_processors:
                    del active_processors[task_id]
                save_tasks()  # 保存任务列表
        
        thread = threading.Thread(target=run_processing)
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'success': True, 
            'task_id': task_id,
            'message': '处理任务已启动'
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'启动失败: {str(e)}'})

@app.route('/api/task_status/<task_id>')
def task_status(task_id):
    """获取任务状态"""
    if task_id not in processing_tasks:
        return jsonify({'success': False, 'message': '任务不存在'})
    
    task = processing_tasks[task_id]
    
    # 获取处理器的进度信息
    progress_info = {
        'processed_count': 0,
        'total_count': 0,
        'current_item': '',
        'success_rate': 0,
        'error_count': 0,
        'is_paused': False,
        'should_stop': False
    }
    
    # 从内存中获取处理器对象
    if task_id in active_processors:
        processor = active_processors[task_id]
        progress_info['processed_count'] = getattr(processor, 'processed_count', 0)
        progress_info['total_count'] = getattr(processor, 'total_count', 0)
        progress_info['current_item'] = getattr(processor, 'current_item', '')
        progress_info['success_rate'] = getattr(processor, 'success_rate', 0)
        progress_info['error_count'] = getattr(processor, 'error_count', 0)
        progress_info['is_paused'] = getattr(processor, 'paused', False)
        progress_info['should_stop'] = getattr(processor, 'should_stop', False)

        # 根据处理器状态更新任务状态
        if getattr(processor, 'should_stop', False) and task['status'] != 'stopped':
            task['status'] = 'stopped'
            task['end_time'] = datetime.now().isoformat()
            save_tasks()
        elif getattr(processor, 'paused', False) and task['status'] == 'running':
            task['status'] = 'paused'
            save_tasks()
        elif not getattr(processor, 'paused', False) and task['status'] == 'paused':
            task['status'] = 'running'
            save_tasks()
    
    return jsonify({
        'success': True,
        'task_id': task_id,
        'status': task['status'],
        'start_time': task.get('start_time'),
        'end_time': task.get('end_time'),
        'filename': task.get('filename'),
        'error': task.get('error'),
        'field_to_process': task.get('field_to_process'),
        'concurrency': task.get('concurrency'),
        'progress': progress_info
    })

@app.route('/api/simulate_progress', methods=['POST'])
def simulate_progress():
    """模拟进度更新（用于测试）"""
    try:
        # 更新第一个运行中任务的进度
        for task_id, task in processing_tasks.items():
            if task.get('status') == 'running':
                current_progress = task.get('progress', 0)
                new_progress = min(current_progress + 10, 100)
                task['progress'] = new_progress
                task['message'] = f'正在处理第{new_progress}/100项...'
                
                if new_progress >= 100:
                    task['status'] = 'completed'
                    task['end_time'] = datetime.now().isoformat()
                    task['message'] = '处理完成'
                
                save_tasks()
                return jsonify({
                    'success': True, 
                    'message': f'进度已更新到 {new_progress}%',
                    'progress': new_progress
                })
        
        return jsonify({'success': False, 'message': '没有运行中的任务'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'更新失败: {str(e)}'})

@app.route('/api/tasks')
def list_tasks():
    """列出所有任务"""
    tasks = []
    for task_id, task in processing_tasks.items():
        # 获取实时进度信息
        progress_data = None
        if 'processor' in task and task['status'] == 'running':
            processor = task['processor']
            try:
                progress_data = {
                    'processed_count': getattr(processor, 'processed_count', 0),
                    'total_count': getattr(processor, 'total_count', 0),
                    'current_item': getattr(processor, 'current_item', ''),
                    'success_rate': getattr(processor, 'success_rate', 0),
                    'error_count': getattr(processor, 'error_count', 0),
                }
                # 计算进度百分比
                if progress_data['total_count'] > 0:
                    progress_percent = (progress_data['processed_count'] / progress_data['total_count']) * 100
                else:
                    progress_percent = 0
            except:
                progress_percent = task.get('progress', 0)
        else:
            progress_percent = task.get('progress', 0)
        
        tasks.append({
            'task_id': task_id,
            'status': task['status'],
            'start_time': task.get('start_time'),
            'end_time': task.get('end_time'),
            'filename': task.get('filename'),
            'error': task.get('error'),
            'progress': progress_percent,
            'message': task.get('message'),
            'field_to_process': task.get('field_to_process'),
            'progress_data': progress_data
        })
    
    return jsonify({'success': True, 'tasks': tasks})

@app.route('/results')
def results():
    """结果页面"""
    return render_template('results.html')

@app.route('/task_manager')
def task_manager():
    """任务管理页面"""
    return render_template('task_manager.html')

@app.route('/api/pause_processing/<task_id>', methods=['POST'])
def pause_processing(task_id):
    """暂停处理"""
    try:
        if task_id not in processing_tasks:
            return jsonify({'success': False, 'message': '任务不存在'})
        
        task = processing_tasks[task_id]
        if task_id not in active_processors:
            return jsonify({'success': False, 'message': '处理器不存在'})

        processor = active_processors[task_id]
        if hasattr(processor, 'pause_processing'):
            processor.pause_processing()
            task['status'] = 'paused'
            save_tasks()
            return jsonify({'success': True, 'message': '处理已暂停'})
        else:
            return jsonify({'success': False, 'message': '处理器不支持暂停功能'})
    
    except Exception as e:
        return jsonify({'success': False, 'message': f'暂停失败: {str(e)}'})

@app.route('/api/resume_processing/<task_id>', methods=['POST'])
def resume_processing(task_id):
    """继续处理"""
    try:
        if task_id not in processing_tasks:
            return jsonify({'success': False, 'message': '任务不存在'})
        
        task = processing_tasks[task_id]
        if task_id not in active_processors:
            return jsonify({'success': False, 'message': '处理器不存在'})

        processor = active_processors[task_id]
        if hasattr(processor, 'resume_processing'):
            processor.resume_processing()
            task['status'] = 'running'
            save_tasks()
            return jsonify({'success': True, 'message': '处理已继续'})
        else:
            return jsonify({'success': False, 'message': '处理器不支持继续功能'})
    
    except Exception as e:
        return jsonify({'success': False, 'message': f'继续失败: {str(e)}'})

@app.route('/api/stop_processing/<task_id>', methods=['POST'])
def stop_processing(task_id):
    """停止处理"""
    try:
        if task_id not in processing_tasks:
            return jsonify({'success': False, 'message': '任务不存在'})
        
        task = processing_tasks[task_id]
        if task_id not in active_processors:
            return jsonify({'success': False, 'message': '处理器不存在'})

        processor = active_processors[task_id]
        if hasattr(processor, 'stop_processing'):
            processor.stop_processing()
            task['status'] = 'stopped'
            task['end_time'] = datetime.now().isoformat()
            # 清理处理器对象
            if task_id in active_processors:
                del active_processors[task_id]
            save_tasks()
            return jsonify({'success': True, 'message': '处理已停止'})
        else:
            return jsonify({'success': False, 'message': '处理器不支持停止功能'})
    
    except Exception as e:
        return jsonify({'success': False, 'message': f'停止失败: {str(e)}'})

@app.route('/api/results')
def api_results():
    """获取处理结果"""
    try:
        output_dir = Path("infini_output")
        
        results = []
        if output_dir.exists():
            for file in output_dir.glob("*.csv"):
                if 'results' in file.name:
                    stat = file.stat()
                    results.append({
                        'filename': file.name,
                        'filepath': str(file),
                        'size': stat.st_size,
                        'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        'modified': datetime.fromtimestamp(stat.st_mtime).isoformat()
                    })
        
        return jsonify({'success': True, 'results': results})
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取结果失败: {str(e)}'})

@app.route('/api/debug_tasks')
def debug_tasks():
    """调试任务信息"""
    debug_info = {
        'tasks_file_exists': os.path.exists(TASKS_FILE),
        'tasks_count': len(processing_tasks),
        'tasks_keys': list(processing_tasks.keys()),
        'raw_tasks': {}
    }
    
    for task_id, task in processing_tasks.items():
        debug_info['raw_tasks'][task_id] = {
            'status': task.get('status'),
            'progress': task.get('progress'),
            'message': task.get('message'),
            'filename': task.get('filename'),
            'has_processor': 'processor' in task,
            'start_time': task.get('start_time'),
            'field_to_process': task.get('field_to_process')
        }
    
    return jsonify(debug_info)

@app.route('/api/checkpoints', methods=['GET'])
def get_checkpoints():
    """获取所有断点信息"""
    try:
        output_dir = Path("infini_output")
        checkpoints = []
        
        if output_dir.exists():
            # 查找所有断点文件
            for file in output_dir.glob("*checkpoint*.json"):
                data_type = None
                field_type = None

                # 解析新格式文件名: infini_processing_checkpoint_{data_type}_{field_type}.json
                if file.name.startswith('infini_processing_checkpoint_'):
                    name_parts = file.name.replace('infini_processing_checkpoint_', '').replace('.json', '').split('_')

                    if len(name_parts) == 2:
                        # 新格式: data_type_field_type
                        data_type, field_type = name_parts
                    elif len(name_parts) == 1:
                        # 旧格式: 只有field_type，默认为movie数据类型
                        field_type = name_parts[0]
                        data_type = 'movie'
                    else:
                        # 兼容其他可能的格式
                        if 'director' in file.name:
                            field_type = 'director'
                        elif 'screenwriter' in file.name:
                            field_type = 'screenwriter'
                        elif 'actor' in file.name:
                            field_type = 'actor'
                        data_type = 'movie'  # 默认数据类型

                if field_type:
                    stat = file.stat()
                    checkpoint_data = {
                        'data_type': data_type or 'movie',
                        'field_type': field_type,
                        'filename': file.name,
                        'filepath': str(file),
                        'size': stat.st_size,
                        'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        'modified': datetime.fromtimestamp(stat.st_mtime).isoformat()
                    }

                    # 尝试读取断点内容
                    try:
                        with open(file, 'r', encoding='utf-8') as f:
                            checkpoint_content = json.load(f)
                            checkpoint_data.update(checkpoint_content)
                    except:
                        checkpoint_data['read_error'] = True

                    checkpoints.append(checkpoint_data)
        
        return jsonify({'success': True, 'checkpoints': checkpoints})
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取断点失败: {str(e)}'})

@app.route('/api/checkpoint/<field_type>', methods=['GET'])
def get_checkpoint(field_type):
    """获取特定字段类型的断点信息"""
    try:
        if field_type not in ['director', 'screenwriter', 'actor']:
            return jsonify({'success': False, 'message': '无效的字段类型'})
        
        output_dir = Path("infini_output")
        checkpoint_file = output_dir / f"infini_processing_checkpoint_{field_type}.json"
        
        if not checkpoint_file.exists():
            return jsonify({'success': False, 'message': '断点文件不存在'})
        
        with open(checkpoint_file, 'r', encoding='utf-8') as f:
            checkpoint_data = json.load(f)
        
        stat = checkpoint_file.stat()
        checkpoint_data.update({
            'field_type': field_type,
            'filename': checkpoint_file.name,
            'filepath': str(checkpoint_file),
            'size': stat.st_size,
            'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
            'modified': datetime.fromtimestamp(stat.st_mtime).isoformat()
        })
        
        return jsonify({'success': True, 'checkpoint': checkpoint_data})
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取断点失败: {str(e)}'})

@app.route('/api/checkpoint/<field_type>/reset', methods=['POST'])
def reset_checkpoint(field_type):
    """重置断点"""
    try:
        if field_type not in ['director', 'screenwriter', 'actor']:
            return jsonify({'success': False, 'message': '无效的字段类型'})

        output_dir = Path("infini_output")
        checkpoint_file = output_dir / f"infini_processing_checkpoint_{field_type}.json"

        if checkpoint_file.exists():
            # 备份原断点文件
            backup_file = output_dir / f"infini_processing_checkpoint_{field_type}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            checkpoint_file.rename(backup_file)

        return jsonify({'success': True, 'message': f'{field_type} 断点已重置'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'重置断点失败: {str(e)}'})

@app.route('/api/checkpoint/<data_type>/<field_type>', methods=['GET'])
def get_checkpoint_by_data_type(data_type, field_type):
    """获取特定数据类型和字段类型的断点信息"""
    try:
        # 验证数据类型
        valid_data_types = ['movie', 'book', 'tv']
        if data_type not in valid_data_types:
            return jsonify({'success': False, 'message': f'无效的数据类型: {data_type}'})

        # 验证字段类型
        valid_field_types = ['director', 'screenwriter', 'actor', 'author', 'translator', 'editor', 'writer', 'cast']
        if field_type not in valid_field_types:
            return jsonify({'success': False, 'message': f'无效的字段类型: {field_type}'})

        output_dir = Path("infini_output")
        checkpoint_file = output_dir / f"infini_processing_checkpoint_{data_type}_{field_type}.json"

        if not checkpoint_file.exists():
            return jsonify({'success': False, 'message': '断点文件不存在'})

        with open(checkpoint_file, 'r', encoding='utf-8') as f:
            checkpoint_data = json.load(f)

        stat = checkpoint_file.stat()
        checkpoint_data.update({
            'data_type': data_type,
            'field_type': field_type,
            'filename': checkpoint_file.name,
            'filepath': str(checkpoint_file),
            'size': stat.st_size,
            'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
            'modified': datetime.fromtimestamp(stat.st_mtime).isoformat()
        })

        return jsonify({'success': True, 'checkpoint': checkpoint_data})
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取断点失败: {str(e)}'})

@app.route('/api/checkpoint/<data_type>/<field_type>/reset', methods=['POST'])
def reset_checkpoint_by_data_type(data_type, field_type):
    """重置特定数据类型和字段类型的断点"""
    try:
        # 验证数据类型
        valid_data_types = ['movie', 'book', 'tv']
        if data_type not in valid_data_types:
            return jsonify({'success': False, 'message': f'无效的数据类型: {data_type}'})

        # 验证字段类型
        valid_field_types = ['director', 'screenwriter', 'actor', 'author', 'translator', 'editor', 'writer', 'cast']
        if field_type not in valid_field_types:
            return jsonify({'success': False, 'message': f'无效的字段类型: {field_type}'})

        output_dir = Path("infini_output")
        checkpoint_file = output_dir / f"infini_processing_checkpoint_{data_type}_{field_type}.json"

        if checkpoint_file.exists():
            # 备份原断点文件
            backup_file = output_dir / f"infini_processing_checkpoint_{data_type}_{field_type}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            checkpoint_file.rename(backup_file)

        return jsonify({'success': True, 'message': f'{data_type} {field_type} 断点已重置'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'重置断点失败: {str(e)}'})

@app.route('/api/checkpoint/<field_type>/export', methods=['GET'])
def export_checkpoint(field_type):
    """导出断点信息"""
    try:
        if field_type not in ['director', 'screenwriter', 'actor']:
            return jsonify({'success': False, 'message': '无效的字段类型'})
        
        output_dir = Path("infini_output")
        checkpoint_file = output_dir / f"infini_processing_checkpoint_{field_type}.json"
        
        if not checkpoint_file.exists():
            return jsonify({'success': False, 'message': '断点文件不存在'})
        
        with open(checkpoint_file, 'r', encoding='utf-8') as f:
            checkpoint_data = json.load(f)
        
        # 创建导出数据
        export_data = {
            'export_time': datetime.now().isoformat(),
            'field_type': field_type,
            'checkpoint_data': checkpoint_data
        }
        
        # 保存导出文件
        export_file = output_dir / f"checkpoint_export_{field_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(export_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False, default=str)
        
        return jsonify({
            'success': True, 
            'message': '断点已导出',
            'export_file': str(export_file),
            'export_data': export_data
        })
    except Exception as e:
        return jsonify({'success': False, 'message': f'导出断点失败: {str(e)}'})


@app.route('/api/file_preview')
def file_preview():
    """文件预览接口"""
    try:
        file_path = request.args.get('file')
        if not file_path:
            return jsonify({'success': False, 'message': '缺少文件路径参数'})
        
        file_path = Path(file_path)
        if not file_path.exists():
            return jsonify({'success': False, 'message': '文件不存在'})
        
        # 获取文件基本信息
        stat = file_path.stat()
        file_info = {
            'size': stat.st_size,
            'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
            'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
            'extension': file_path.suffix
        }
        
        preview_data = None
        
        # 根据文件类型生成预览
        if file_path.suffix == '.csv':
            import pandas as pd
            df = pd.read_csv(file_path)
            file_info['record_count'] = len(df)
            preview_data = df.head(10).to_dict('records')
        
        elif file_path.suffix == '.json':
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if isinstance(data, list):
                    file_info['record_count'] = len(data)
                    preview_data = data[:10] if len(data) > 10 else data
                else:
                    file_info['record_count'] = 1
                    preview_data = [data] if data else []
        
        elif file_path.suffix in ['.txt', '.log']:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                file_info['record_count'] = len(lines)
                preview_data = [{'line': i+1, 'content': line.strip()} for i, line in enumerate(lines[:10])]
        
        return jsonify({
            'success': True,
            'file_info': file_info,
            'preview': preview_data
        })
    
    except Exception as e:
        ErrorHandler.log_error('FILE_PREVIEW_ERROR', str(e))
        return jsonify({'success': False, 'message': f'文件预览失败: {str(e)}'})

@app.route('/api/download')
def download_file():
    """文件下载接口"""
    try:
        from flask import send_file
        
        file_path = request.args.get('file')
        if not file_path:
            return jsonify({'success': False, 'message': '缺少文件路径参数'})
        
        file_path = Path(file_path)
        if not file_path.exists():
            return jsonify({'success': False, 'message': '文件不存在'})
        
        return send_file(file_path, as_attachment=True, download_name=file_path.name)
    
    except Exception as e:
        ErrorHandler.log_error('DOWNLOAD_ERROR', str(e))
        return jsonify({'success': False, 'message': f'文件下载失败: {str(e)}'})

@app.route('/api/delete_file', methods=['POST'])
def delete_file():
    """删除文件接口"""
    try:
        data = request.get_json()
        file_path = data.get('filepath')
        
        if not file_path:
            return jsonify({'success': False, 'message': '缺少文件路径参数'})
        
        file_path = Path(file_path)
        if not file_path.exists():
            return jsonify({'success': False, 'message': '文件不存在'})
        
        # 安全检查 - 只允许删除输出目录中的文件
        if not str(file_path).startswith('infini_output'):
            return jsonify({'success': False, 'message': '无权限删除此文件'})
        
        # 创建备份目录
        backup_dir = Path('backups')
        backup_dir.mkdir(exist_ok=True)
        
        # 移动文件到备份目录而不是直接删除
        backup_path = backup_dir / f"{file_path.stem}_{datetime.now().strftime('%Y%m%d_%H%M%S')}{file_path.suffix}"
        file_path.rename(backup_path)
        
        return jsonify({'success': True, 'message': '文件已删除并备份'})
    
    except Exception as e:
        ErrorHandler.log_error('DELETE_FILE_ERROR', str(e))
        return jsonify({'success': False, 'message': f'删除文件失败: {str(e)}'})

@app.route('/api/export_results', methods=['POST'])
def export_results():
    """导出结果接口"""
    try:
        data = request.get_json()
        export_format = data.get('format', 'csv')
        
        if export_format not in ['csv', 'json']:
            return jsonify({'success': False, 'message': '不支持的导出格式'})
        
        output_dir = Path("infini_output")
        export_dir = Path("exports")
        export_dir.mkdir(exist_ok=True)
        
        # 收集所有结果文件
        all_results = []
        
        if output_dir.exists():
            for csv_file in output_dir.glob("*results*.csv"):
                try:
                    import pandas as pd
                    df = pd.read_csv(csv_file)
                    df['source_file'] = csv_file.name
                    all_results.append(df)
                except Exception as e:
                    logger.error(f"读取文件 {csv_file} 失败: {str(e)}")
        
        if not all_results:
            return jsonify({'success': False, 'message': '没有可导出的结果文件'})
        
        # 合并所有结果
        import pandas as pd
        combined_df = pd.concat(all_results, ignore_index=True)
        
        # 导出文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        if export_format == 'csv':
            export_file = export_dir / f"combined_results_{timestamp}.csv"
            combined_df.to_csv(export_file, index=False, encoding='utf-8')
        else:  # json
            export_file = export_dir / f"combined_results_{timestamp}.json"
            combined_df.to_json(export_file, orient='records', force_ascii=False, indent=2)
        
        return jsonify({
            'success': True,
            'message': f'结果已导出为 {export_format.upper()}',
            'export_file': str(export_file),
            'record_count': len(combined_df)
        })
    
    except Exception as e:
        ErrorHandler.log_error('EXPORT_RESULTS_ERROR', str(e))
        return jsonify({'success': False, 'message': f'导出失败: {str(e)}'})

@app.route('/api/clear_results', methods=['POST'])
def clear_results():
    """清理所有结果文件"""
    try:
        output_dir = Path("infini_output")
        backup_dir = Path("backups")
        backup_dir.mkdir(exist_ok=True)
        
        cleared_count = 0
        
        if output_dir.exists():
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # 移动所有文件到备份目录
            for file in output_dir.glob("*"):
                if file.is_file():
                    backup_path = backup_dir / f"{file.stem}_{timestamp}{file.suffix}"
                    file.rename(backup_path)
                    cleared_count += 1
        
        return jsonify({
            'success': True,
            'message': f'已清理 {cleared_count} 个文件并备份'
        })
    
    except Exception as e:
        ErrorHandler.log_error('CLEAR_RESULTS_ERROR', str(e))
        return jsonify({'success': False, 'message': f'清理失败: {str(e)}'})

@app.route('/api/test_connection', methods=['POST'])
def test_connection():
    """测试API连接"""
    try:
        data = request.get_json()
        api_key = data.get('api_key')
        api_url = data.get('api_url')
        model = data.get('model')

        if not all([api_key, api_url, model]):
            return jsonify({'success': False, 'message': '缺少必要的连接参数'})

        # 这里可以添加实际的API连接测试逻辑
        # 暂时返回成功（实际项目中应该发送测试请求）

        return jsonify({
            'success': True,
            'message': '连接测试成功',
            'details': {
                'api_url': api_url,
                'model': model,
                'response_time': '156ms'
            }
        })

    except Exception as e:
        ErrorHandler.log_error('CONNECTION_TEST_ERROR', str(e))
        return jsonify({'success': False, 'message': f'连接测试失败: {str(e)}'})

@app.route('/test')
def test_page():
    """测试页面"""
    return send_from_directory('.', 'test_page.html')

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)