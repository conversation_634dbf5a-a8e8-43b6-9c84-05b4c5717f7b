#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Douban Movie Data Processor using Infini-AI API
---------------------------------------------
本脚本处理电影数据，提取导演/编剧详细信息，使用 Infini-AI API。
"""
import os
import json
import time
import asyncio
import pandas as pd
import logging
import concurrent.futures
import argparse
from typing import Dict, List, Optional, Tuple, Any, Union
from pathlib import Path
from tqdm import tqdm
import sys
from datetime import datetime
from openai import AsyncOpenAI

# Configure logging
from datetime import datetime

# 创建带时间戳的日志文件名
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
log_dir = "logs"
log_file = f"{log_dir}/infini_processing_{timestamp}.log"
console_log_file = f"{log_dir}/infini_console_{timestamp}.log"

# 确保logs目录存在
import os
os.makedirs(log_dir, exist_ok=True)

# 创建根日志记录器
root_logger = logging.getLogger()
root_logger.setLevel(logging.INFO)

# 清除可能存在的处理程序
for handler in root_logger.handlers[:]:
    root_logger.removeHandler(handler)

# 创建文件处理程序
file_handler = logging.FileHandler(log_file, encoding='utf-8')
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
root_logger.addHandler(file_handler)

# 创建控制台处理程序
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
# 处理Windows编码问题
if sys.platform == 'win32':
    console_handler.stream.reconfigure(encoding='utf-8', errors='backslashreplace')
root_logger.addHandler(console_handler)

# 创建专门用于保存控制台输出的文件处理程序
console_file_handler = logging.FileHandler(console_log_file, encoding='utf-8')
console_file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
root_logger.addHandler(console_file_handler)

logger = logging.getLogger("infini_processor")
logger.info(f"日志文件已创建: {log_file}, {console_log_file}")

# Constants
CONFIG_FILE = "infini_config.json"

class InfiniDirectorProcessor:
    def __init__(self, api_keys: List[str], data_path: str, output_dir: str,
                 max_workers: int = 50,
                 batch_size: int = 100, field_to_process: str = "director",
                 data_type: str = "movie",
                 test_mode: bool = False, test_limit: int = 50, min_reviews: int = 0,
                 min_short_reviews: int = 0,
                 retry_refused: bool = True, retry_failed: bool = True,
                 export_only: bool = False, export_current_results: bool = False,
                 config: Dict = None):
        self.api_keys = api_keys
        self.data_path = data_path
        self.output_dir = Path(output_dir)
        self.max_workers = max_workers
        self.batch_size = batch_size
        self.field_to_process = field_to_process
        self.data_type = data_type
        self.test_mode = test_mode
        self.test_limit = test_limit
        self.min_reviews = int(min_reviews)
        self.min_short_reviews = int(min_short_reviews)
        self.retry_refused = retry_refused
        self.retry_failed = retry_failed
        self.export_only = export_only
        self.export_current_results = export_current_results

        # 设置数据类型映射
        self.config = config or {}
        if 'data_type_mappings' in self.config and data_type in self.config['data_type_mappings']:
            mapping = self.config['data_type_mappings'][data_type]
            self.field_mappings = mapping['fields']
            self.title_field = mapping['title_field']
        else:
            # 默认映射（向后兼容）
            self.field_mappings = {
                'director': 'daoyan',
                'screenwriter': 'bianjv',
                'actor': 'zhuyan'
            }
            self.title_field = 'biaoti'

        # 根据数据类型和处理字段类型设置文件名
        self.checkpoint_file = f"infini_processing_checkpoint_{self.data_type}_{self.field_to_process}.json"
        self.results_file = f"infini_processing_results_{self.data_type}_{self.field_to_process}.json"
        self.error_log_file = f"infini_processing_errors_{self.data_type}_{self.field_to_process}.json"
        self.refused_requests_file = f"infini_refused_requests_{self.data_type}_{self.field_to_process}.json"

        # 设置数据库文件路径
        if self.field_to_process == "actor":
            self.database_file = f"{self.data_type}_actors_database.json"
        else:
            self.database_file = f"{self.data_type}_{self.field_to_process}s_database.json"

        self.output_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"处理字段: {self.field_to_process}")
        logger.info(f"使用文件: 检查点={self.checkpoint_file}, 结果={self.results_file}, 错误={self.error_log_file}, 拒绝请求={self.refused_requests_file}")
        logger.info(f"数据库文件: {self.database_file}")
        
        # 初始化进度跟踪属性
        self.processed_count = 0
        self.total_count = 0
        self.current_item = ''
        self.success_rate = 0.0
        self.error_count = 0
        
        # 暂停控制
        self.paused = False
        self.should_stop = False

        # 检查并迁移旧的通用文件数据
        self._migrate_legacy_files()

        self.checkpoint = self._load_checkpoint()
        self.results = self._load_results()

        # 加载已处理的数据库
        self.processed_database = self._load_processed_database()
        self.errors = self._load_errors()
        self.refused_requests = self._load_refused_requests()
        self.data_loaded = False
        self.df = None
        
        # 根据API提供商设置不同的客户端
        if config:
            api_provider = config.get("api_provider", "infini")
            api_base_url = config.get("api_base_url", "https://cloud.infini-ai.com/maas/v1/")
            api_model = config.get("api_model", "deepseek-r1")
        else:
            api_provider = "infini"
            api_base_url = "https://cloud.infini-ai.com/maas/v1/"
            api_model = "deepseek-r1"
        
        self.api_provider = api_provider
        self.api_model = api_model
        
        if api_provider == "zhipu":
            self.clients = [AsyncOpenAI(api_key=key, base_url=api_base_url) for key in self.api_keys]
        else:
            self.clients = [AsyncOpenAI(api_key=key, base_url=api_base_url) for key in self.api_keys]
            
        self.api_request_counter = 0
        self.disabled_api_keys = set()
        self.last_key_check_day = datetime.now().day

        # 数据库更新计数器
        self.database_update_counter = 0
        self.database_update_interval = 900  # 每处理900个数据更新一次数据库
        if self.test_mode:
            logger.warning(f"RUNNING IN TEST MODE - Processing limited to {self.test_limit} records")

    def _migrate_legacy_files(self) -> None:
        """迁移旧的通用文件数据到新的字段特定文件中"""
        legacy_files = {
            "checkpoint": "infini_processing_checkpoint.json",
            "results": "infini_processing_results.json",
            "errors": "infini_processing_errors.json",
            "refused": "infini_refused_requests.json"
        }

        migrated_any = False

        for file_type, legacy_filename in legacy_files.items():
            legacy_path = self.output_dir / legacy_filename
            if not legacy_path.exists():
                continue

            # 确定新文件路径
            if file_type == "checkpoint":
                new_path = self.output_dir / self.checkpoint_file
            elif file_type == "results":
                new_path = self.output_dir / self.results_file
            elif file_type == "errors":
                new_path = self.output_dir / self.error_log_file
            elif file_type == "refused":
                new_path = self.output_dir / self.refused_requests_file
            else:
                continue

            # 如果新文件已存在，跳过迁移
            if new_path.exists():
                logger.info(f"新文件 {new_path.name} 已存在，跳过从 {legacy_filename} 的迁移")
                continue

            try:
                # 读取旧文件数据
                with open(legacy_path, 'r', encoding='utf-8') as f:
                    legacy_data = json.load(f)

                if file_type == "checkpoint":
                    # 检查点文件直接迁移，但更新字段类型
                    if isinstance(legacy_data, dict):
                        legacy_data["current_field"] = self.field_to_process
                        with open(new_path, 'w', encoding='utf-8') as f:
                            json.dump(legacy_data, f, ensure_ascii=False, indent=2, default=str)
                        logger.info(f"已迁移检查点文件: {legacy_filename} -> {new_path.name}")
                        migrated_any = True

                elif file_type == "results":
                    # 结果文件需要根据字段类型过滤
                    filtered_data = self._filter_results_by_field(legacy_data)
                    if filtered_data:
                        with open(new_path, 'w', encoding='utf-8') as f:
                            json.dump(filtered_data, f, ensure_ascii=False, indent=2, default=str)
                        logger.info(f"已迁移结果文件: {legacy_filename} -> {new_path.name} (过滤出 {len(filtered_data)} 条记录)")
                        migrated_any = True

                elif file_type in ["errors", "refused"]:
                    # 错误和拒绝请求文件也需要根据字段类型过滤
                    filtered_data = self._filter_results_by_field(legacy_data)
                    if filtered_data:
                        with open(new_path, 'w', encoding='utf-8') as f:
                            json.dump(filtered_data, f, ensure_ascii=False, indent=2, default=str)
                        logger.info(f"已迁移{file_type}文件: {legacy_filename} -> {new_path.name} (过滤出 {len(filtered_data)} 条记录)")
                        migrated_any = True

            except Exception as e:
                logger.error(f"迁移文件 {legacy_filename} 时出错: {str(e)}")

        # 如果有迁移数据，创建备份并提供说明
        if migrated_any:
            self._create_migration_backup()
            logger.info(f"数据迁移完成，当前处理字段: {self.field_to_process}")
            logger.info("原始文件已保留，如需回滚可手动恢复")
        else:
            logger.debug(f"无需迁移数据，当前处理字段: {self.field_to_process}")

    def _create_migration_backup(self) -> None:
        """为迁移的文件创建备份说明"""
        backup_info = {
            "migration_date": datetime.now().isoformat(),
            "field_processed": self.field_to_process,
            "note": "数据已从通用文件迁移到字段特定文件。原始文件仍保留在同目录下。",
            "original_files": [
                "infini_processing_checkpoint.json",
                "infini_processing_results.json",
                "infini_processing_errors.json",
                "infini_refused_requests.json"
            ],
            "new_files": [
                self.checkpoint_file,
                self.results_file,
                self.error_log_file,
                self.refused_requests_file
            ]
        }

        backup_path = self.output_dir / f"migration_info_{self.field_to_process}.json"
        try:
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, ensure_ascii=False, indent=2, default=str)
            logger.info(f"迁移信息已保存到: {backup_path.name}")
        except Exception as e:
            logger.error(f"保存迁移信息时出错: {str(e)}")

    def _filter_results_by_field(self, data: Dict) -> Dict:
        """根据当前字段类型过滤结果数据"""
        if not isinstance(data, dict):
            return {}

        filtered_data = {}
        for key, value in data.items():
            should_include = False

            if isinstance(value, dict):
                # 方法1: 检查role字段
                if 'role' in value and value['role'] == self.field_to_process:
                    should_include = True
                # 方法2: 检查extracted_data中的profession字段
                elif 'extracted_data' in value and isinstance(value['extracted_data'], dict):
                    profession = value['extracted_data'].get('profession', '')
                    if profession == self.field_to_process:
                        should_include = True
                # 方法3: 如果没有明确的字段标识，检查数据是否来自当前处理的数据源
                # 这种情况下，我们假设旧数据主要是导演数据（因为这是默认值）
                elif 'role' not in value and self.field_to_process == 'director':
                    should_include = True
                    # 为旧数据添加role字段
                    value['role'] = 'director'
            else:
                # 对于非字典类型的数据，如果当前处理导演字段则包含（兼容旧数据）
                if self.field_to_process == 'director':
                    should_include = True

            if should_include:
                filtered_data[key] = value

        return filtered_data

    def _load_checkpoint(self) -> Dict:
        checkpoint_path = self.output_dir / self.checkpoint_file
        if checkpoint_path.exists():
            try:
                with open(checkpoint_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading checkpoint: {str(e)}")
        return {
            "current_field": self.field_to_process,
            "last_processed_index": 0,
            "processed_ids": []
        }

    def _save_checkpoint(self) -> None:
        checkpoint_path = self.output_dir / self.checkpoint_file
        try:
            self.output_dir.mkdir(parents=True, exist_ok=True)
            with open(checkpoint_path, 'w', encoding='utf-8') as f:
                json.dump(self.checkpoint, f, ensure_ascii=False, indent=2, default=str)
            logger.debug(f"Checkpoint saved to {checkpoint_path}")
        except Exception as e:
            logger.error(f"Error saving checkpoint: {str(e)}")

    def _load_results(self) -> Dict:
        results_path = self.output_dir / self.results_file
        if results_path.exists():
            try:
                with open(results_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading results: {str(e)}")
        return {}

    def _save_results(self) -> None:
        results_path = self.output_dir / self.results_file
        try:
            self.output_dir.mkdir(parents=True, exist_ok=True)
            results_copy = dict(self.results)
            with open(results_path, 'w', encoding='utf-8') as f:
                json.dump(results_copy, f, ensure_ascii=False, indent=2, default=str)
            logger.debug(f"Results saved to {results_path}")
        except Exception as e:
            logger.error(f"Error saving results: {str(e)}")

    def _load_errors(self) -> Dict:
        errors_path = self.output_dir / self.error_log_file
        if errors_path.exists():
            try:
                with open(errors_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading errors: {str(e)}")
        return {}

    def _save_errors(self) -> None:
        errors_path = self.output_dir / self.error_log_file
        try:
            self.output_dir.mkdir(parents=True, exist_ok=True)
            with open(errors_path, 'w', encoding='utf-8') as f:
                json.dump(self.errors, f, ensure_ascii=False, indent=2, default=str)
            logger.debug(f"Errors saved to {errors_path}")
        except Exception as e:
            logger.error(f"Error saving errors: {str(e)}")

    def _load_refused_requests(self) -> Dict:
        refused_path = self.output_dir / self.refused_requests_file
        if refused_path.exists():
            try:
                with open(refused_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading refused requests: {str(e)}")
        return {}

    def _save_refused_requests(self) -> None:
        refused_path = self.output_dir / self.refused_requests_file
        try:
            with open(refused_path, 'w', encoding='utf-8') as f:
                json.dump(self.refused_requests, f, ensure_ascii=False, indent=2, default=str)
            logger.debug(f"Refused requests saved to {refused_path}")
        except Exception as e:
            logger.error(f"Error saving refused requests: {str(e)}")

    def _load_processed_database(self) -> Dict[str, Any]:
        """加载已处理的数据库文件"""
        try:
            database_path = Path(self.database_file)
            if database_path.exists():
                with open(database_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                logger.info(f"加载已处理数据库: {self.database_file}, 包含 {len(data)} 条记录")
                return data
            else:
                logger.info(f"未找到数据库文件: {self.database_file}，将处理所有记录")
                return {}
        except Exception as e:
            logger.error(f"加载数据库文件时出错: {str(e)}")
            return {}

    def _save_processed_database(self) -> None:
        """保存已处理的数据库文件"""
        try:
            database_path = Path(self.database_file)
            with open(database_path, 'w', encoding='utf-8') as f:
                json.dump(self.processed_database, f, ensure_ascii=False, indent=2, default=str)
            logger.info(f"已更新数据库文件: {self.database_file}, 包含 {len(self.processed_database)} 条记录")
        except Exception as e:
            logger.error(f"保存数据库文件时出错: {str(e)}")

    def _update_processed_database_from_results(self) -> None:
        """从当前结果更新已处理数据库"""
        updated_count = 0
        for result_key, result_data in self.results.items():
            if isinstance(result_data, dict) and 'extracted_data' in result_data and 'error' not in result_data:
                # 解析结果键值 (movie_id_person_name)
                try:
                    parts = result_key.split('_', 1)
                    if len(parts) == 2:
                        movie_id = int(parts[0])
                        person_name = parts[1]

                        # 生成数据库键值
                        database_key = self._generate_database_key(movie_id, person_name, self.field_to_process)

                        # 如果数据库中还没有这条记录，则添加
                        if database_key not in self.processed_database:
                            self.processed_database[database_key] = result_data
                            updated_count += 1
                except (ValueError, IndexError) as e:
                    logger.warning(f"解析结果键值失败: {result_key}, 错误: {str(e)}")
                    continue

        if updated_count > 0:
            logger.info(f"从结果文件更新了 {updated_count} 条记录到数据库")
            self.database_update_counter += updated_count

    def _all_api_keys_disabled(self) -> bool:
        """检查是否所有API密钥都被禁用"""
        return len(self.disabled_api_keys) >= len(self.clients)

    def _save_and_exit_on_all_disabled(self) -> None:
        """当所有API密钥都被禁用时保存进度并退出"""
        logger.error("所有API密钥都已被禁用，保存当前进度并停止处理")

        # 保存所有进度
        self._save_checkpoint()
        self._save_results()
        self._save_errors()
        self._save_refused_requests()

        # 更新数据库
        self._update_processed_database_from_results()
        self._save_processed_database()

        logger.info("进度已保存，程序将退出")
        raise SystemExit("所有API密钥都已被禁用，程序已停止")

    def _generate_database_key(self, movie_id: int, person_name: str, role: str) -> str:
        """生成与数据库一致的键值格式: {movie_id}_{person_name}_{role}"""
        return f"{movie_id}_{person_name}_{role}"

    def _is_record_processed(self, movie_id: int, person_name: str) -> bool:
        """检查记录是否已在数据库中处理过"""
        database_key = self._generate_database_key(movie_id, person_name, self.field_to_process)
        return database_key in self.processed_database

    def _should_process_record(self, movie_id: int, person_name: str) -> bool:
        """判断是否应该处理该记录（增量处理逻辑）"""
        # 检查当前结果文件（处理过程中的临时结果）
        result_key = f"{movie_id}_{person_name}"
        if result_key in self.results:
            # 进一步检查结果是否成功（有extracted_data且不是错误）
            result_data = self.results[result_key]
            if isinstance(result_data, dict):
                # 如果有extracted_data且没有error，认为已成功处理
                if 'extracted_data' in result_data and 'error' not in result_data:
                    return False
                # 如果只有error，认为需要重新处理
                elif 'error' in result_data and 'extracted_data' not in result_data:
                    return True
            return False

        # 如果结果文件为空，则检查最终数据库文件
        if not self.results and self._is_record_processed(movie_id, person_name):
            return False

        return True

    def get_field_name(self, field_type: str) -> str:
        """根据数据类型获取对应的字段名"""
        return self.field_mappings.get(field_type, field_type)

    def get_title_field(self) -> str:
        """获取标题字段名"""
        return self.title_field

    def load_data(self) -> None:
        if self.data_loaded:
            return
        logger.info("Loading data...")
        path = Path(self.data_path)
        if path.is_dir():
            logger.info(f"Loading data from directory: {path}")
            all_files = list(path.glob("*.csv"))
            if not all_files:
                raise ValueError(f"No CSV files found in {path}")
            dfs = []
            for file in tqdm(all_files, desc="Loading CSV files"):
                try:
                    df = pd.read_csv(file, low_memory=False)
                    dfs.append(df)
                except Exception as e:
                    logger.error(f"Error loading {file}: {str(e)}")
            self.df = pd.concat(dfs, ignore_index=True)
        elif path.suffix.lower() == '.csv':
            logger.info(f"Loading data from CSV file: {path}")
            self.df = pd.read_csv(path, low_memory=False)
        elif path.suffix.lower() == '.pkl':
            logger.info(f"Loading data from pickle file: {path}")
            self.df = pd.read_pickle(path)
        else:
            raise ValueError(f"Unsupported data path: {path}. Must be a directory of CSV files, a CSV file, or a pickle file.")
        logger.info(f"Loaded {len(self.df)} rows of data")
        
        # 处理评价数筛选
        if 'pingjiaShu' in self.df.columns:
            original_length = len(self.df)
            try:
                # 记录NaN值的数量
                nan_count = self.df['pingjiaShu'].isna().sum()
                if nan_count > 0:
                    logger.info(f"发现 {nan_count} 行评价数为NaN")
                
                # 将NaN值替换为0
                self.df['pingjiaShu'] = self.df['pingjiaShu'].fillna(0)
                
                # 确保pingjiaShu列为float64类型
                self.df['pingjiaShu'] = self.df['pingjiaShu'].astype('float64')
                
                # 记录过滤前的行数
                before_filter = len(self.df)
                
                if self.min_reviews > 0:
                    # 使用float64类型进行比较
                    mask = self.df['pingjiaShu'] >= float(self.min_reviews)
                    self.df = self.df[mask]
                    removed = before_filter - len(self.df)
                    logger.info(f"过滤后保留 {len(self.df)} 行数据（评价数大于等于 {self.min_reviews}），移除了 {removed} 行")
                else:
                    logger.info(f"保留所有 {len(self.df)} 行数据（min_reviews = {self.min_reviews}）")
            except Exception as e:
                logger.error(f"处理 pingjiaShu 列时出错: {str(e)}")
                logger.warning("将跳过评价数过滤，使用原始数据继续处理")
        else:
            logger.warning("Column 'pingjiaShu' not found in the data. No filtering applied.")
            
        # 处理短评数筛选
        if 'duanpingShu' in self.df.columns and self.min_short_reviews > 0:
            before_filter = len(self.df)
            try:
                # 记录NaN值的数量
                nan_count = self.df['duanpingShu'].isna().sum()
                if nan_count > 0:
                    logger.info(f"发现 {nan_count} 行短评数为NaN")
                
                # 将NaN值替换为0
                self.df['duanpingShu'] = self.df['duanpingShu'].fillna(0)
                
                # 确保duanpingShu列为float64类型
                self.df['duanpingShu'] = self.df['duanpingShu'].astype('float64')
                
                # 使用float64类型进行比较
                mask = self.df['duanpingShu'] >= float(self.min_short_reviews)
                self.df = self.df[mask]
                removed = before_filter - len(self.df)
                logger.info(f"过滤后保留 {len(self.df)} 行数据（短评数大于等于 {self.min_short_reviews}），移除了 {removed} 行")
            except Exception as e:
                logger.error(f"处理 duanpingShu 列时出错: {str(e)}")
                logger.warning("将跳过短评数过滤，使用原始数据继续处理")
        elif self.min_short_reviews > 0:
            logger.warning("Column 'duanpingShu' not found in the data. No short reviews filtering applied.")
            
        if self.test_mode and len(self.df) > self.test_limit:
            logger.warning(f"TEST MODE: Limiting to {self.test_limit} records from {len(self.df)} total records")
            self.df = self.df.head(self.test_limit)
        # 根据数据类型和处理字段确定必需的列
        base_required_cols = ['ID']
        title_field = self.get_title_field()
        field_name = self.get_field_name(self.field_to_process)

        required_cols = base_required_cols + [title_field, field_name]
        missing_cols = [col for col in required_cols if col not in self.df.columns]
        if missing_cols:
            logger.error(f"数据类型 '{self.data_type}' 缺少必需的列: {missing_cols}")
            logger.info(f"可用的列: {list(self.df.columns)}")
            logger.info(f"期望的标题字段: {title_field}")
            logger.info(f"期望的处理字段: {field_name} (对应 {self.field_to_process})")
            raise ValueError(f"Missing required columns for data type '{self.data_type}': {missing_cols}")
        if 'ID' in self.df.columns:
            self.df = self.df.set_index('ID', drop=False)
        self.data_loaded = True

    def _check_and_reset_api_keys(self) -> None:
        """检查是否过了午夜12点，如果是新的一天则重置所有禁用的API密钥，并重新加载配置"""
        current_day = datetime.now().day
        if current_day != self.last_key_check_day:
            # 重新加载配置
            try:
                config = self._load_config()
                if config and "api_keys" in config:
                    # 更新API密钥列表
                    self.api_keys = config["api_keys"]
                    # 重新创建API客户端
                    self.clients = [AsyncOpenAI(api_key=key, base_url="https://cloud.infini-ai.com/maas/v1/") for key in self.api_keys]
                    logger.info(f"已重新加载配置，当前可用API密钥数量: {len(self.api_keys)}")
            except Exception as e:
                logger.error(f"重新加载配置时出错: {str(e)}")
            
            if self.disabled_api_keys:
                logger.info(f"新的一天已开始，重置所有被禁用的API密钥 ({len(self.disabled_api_keys)}个)")
                self.disabled_api_keys.clear()
                # 记录API密钥重置
                reset_path = self.output_dir / "api_keys_reset.json"
                with open(reset_path, 'a', encoding='utf-8') as f:
                    json.dump({
                        "reset_time": datetime.now().isoformat(),
                        "message": "日期变更，所有API密钥已重置"
                    }, f, ensure_ascii=False)
                    f.write("\n")  # 添加换行符以便追加
            self.last_key_check_day = current_day

    def _load_config(self, config_path=CONFIG_FILE) -> Dict:
        """加载配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            配置字典
        """
        config_path = Path(config_path)
        if not config_path.exists():
            logger.error(f"配置文件不存在: {config_path}")
            return {}
            
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            if "api_keys" not in config or not config["api_keys"]:
                logger.error(f"配置文件中未找到有效的API密钥")
                return {}
            return config
        except json.JSONDecodeError as e:
            logger.error(f"解析配置文件时出错: {str(e)}")
            return {}
        except Exception as e:
            logger.error(f"加载配置文件时出错: {str(e)}")
            return {}

    async def _query_infini_api(self, movie_title: str, person_name: str, client_idx: Optional[int] = None) -> Dict:
        # 检查是否需要重置API密钥
        self._check_and_reset_api_keys()
        
        if not person_name or pd.isna(person_name) or person_name.strip() == '':
            return {'error': 'Empty person name'}
        
        if self.field_to_process == "director":
            role_english = "director"
        elif self.field_to_process == "screenwriter":
            role_english = "screenwriter"
        elif self.field_to_process == "actor":
            role_english = "actor"
        else:
            raise ValueError(f"Unsupported field_to_process: {self.field_to_process}")
        # 根据不同角色设置特定的提示词
        role_prompts = {
            "director": {
                "system": "你是一个专业的数据提取助手，请严格遵循输出格式。你需要搜索并提取电影导演的详细信息。",
                "user_intro": f"""请搜索并提取{person_name}（电影"{movie_title}"的导演）的详细信息。

请提供以下信息，并以JSON格式返回，使用以下字段名：""",
                "specific_requirements": """
导演特定要求：
- 重点关注导演的电影作品和风格
- 教育背景对于导演尤为重要
- 职业描述应包含导演风格和代表作"""
            },
            "screenwriter": {
                "system": "你是一个专业的数据提取助手，请严格遵循输出格式。你需要搜索并提取电影编剧的详细信息。",
                "user_intro": f"""请搜索并提取{person_name}（电影"{movie_title}"的编剧）的详细信息。

请提供以下信息，并以JSON格式返回，使用以下字段名：""",
                "specific_requirements": """
编剧特定要求：
- 重点关注编剧的剧本创作经历
- 文学背景和教育经历对编剧很重要
- 职业描述应包含编剧风格和代表作品"""
            },
            "actor": {
                "system": "你是一个专业的数据提取助手，请严格遵循输出格式。你需要搜索并提取电影演员的详细信息。",
                "user_intro": f"""请搜索并提取{person_name}（电影"{movie_title}"的演员）的详细信息。

请提供以下信息，并以JSON格式返回，使用以下字段名：""",
                "specific_requirements": """
演员特定要求：
- 重点关注演员的表演经历和代表作品
- 演技训练和教育背景
- 职业描述应包含演员类型和表演风格"""
            }
        }
        
        # 获取当前角色的提示词
        if role_english in role_prompts:
            prompt_config = role_prompts[role_english]
            system_message = prompt_config["system"]
            user_message = prompt_config["user_intro"]
        else:
            # 默认提示词
            system_message = f"""你是一个专业的数据提取助手，请严格遵循输出格式。你需要搜索并提取电影{role_english}的详细信息。"""
            user_message = f"""请搜索并提取{person_name}（电影"{movie_title}"的{role_english}）的详细信息。

请提供以下信息，并以JSON格式返回，使用以下字段名："""

        # 基础字段要求
        base_fields = """

1. chinese_name: 中文名（如果没有官方中文名，则使用音译）
2. english_name: 英文名（如果没有官方英文名，则使用音译/拼音）
3. birth_date: 出生日期（YYYY-MM-DD格式，如果只知道年份则只返回YYYY）
4. education_institution: 教育机构（英文）
5. has_university_education: 是否接受过大学教育（布尔值）
6. profession: 主要职业（英文）
7. birth_country: 出生国家（使用ISO三字母国家代码，如CHN、USA）
8. birth_city: 出生城市（英文）
9. gender: 性别（"male"、"female"或null）
10. nationality: 国籍（使用ISO三字母国家代码）"""

        # 组合完整提示词
        user_message += base_fields
        if role_english in role_prompts:
            user_message += prompt_config["specific_requirements"]
        
        user_message += """

请只返回JSON格式的数据。对于未知信息使用null。如果可能，请简要说明为什么某些信息无法获取。"""

        response_metadata = {
            "request_time": datetime.now().isoformat(),
            "model": self.api_model,
            "movie_title": movie_title,
            "person_name": person_name,
            "role": self.field_to_process,
        }

        # 获取可用的API密钥
        available_clients = [i for i in range(len(self.clients)) if i not in self.disabled_api_keys]
        if not available_clients:
            logger.error("所有API密钥都已被禁用，无法继续处理")
            response_metadata["error"] = "所有API密钥都已被禁用，无法继续处理"
            return response_metadata

        # 确定要使用的客户端
        if client_idx is not None:
            # 使用指定的客户端
            if client_idx in self.disabled_api_keys:
                safe_client_idx = self._safe_encode_string(str(client_idx+1))
                logger.warning(f"指定的API密钥 {safe_client_idx} 已被禁用，将尝试使用其他密钥")
                client_idx = available_clients[0]  # 使用第一个可用密钥
            client = self.clients[client_idx]
            logger.debug(f"Using specified API key {client_idx+1}/{len(self.clients)}")
        else:
            # 如果未指定客户端，则从可用客户端中轮询选择
            client_idx = available_clients[self.api_request_counter % len(available_clients)]
            client = self.clients[client_idx]
            self.api_request_counter += 1
            logger.debug(f"Using API key {client_idx+1}/{len(self.clients)}")

        try:
            # 使用安全的字符串格式化，避免编码问题
            safe_movie = self._safe_encode_string(movie_title)
            safe_person = self._safe_encode_string(person_name)
            logger.debug(f"API Request for {safe_movie} - {safe_person}")
            request_start_time = time.time()
            
            response = await client.chat.completions.create(
                model=self.api_model,
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ],
                temperature=0.1,
                response_format={"type": "json_object"}
            )
            
            response_time = time.time() - request_start_time
            response_metadata["response_time_seconds"] = response_time
            response_metadata["response_timestamp"] = datetime.now().isoformat()
            response_metadata["raw_response"] = response.model_dump() if hasattr(response, 'model_dump') else str(response)

            if hasattr(response, 'choices') and len(response.choices) > 0:
                content = response.choices[0].message.content
                response_metadata["message_content"] = content

                # 检测API拒绝回答的情况
                refusal_phrases = [
                    "非常抱歉，作为一个AI助手，我无法回答该问题",
                    "抱歉，我无法回答这个问题",
                    "我不能提供相关信息"
                ]

                # 检查content是否为None，避免NoneType错误
                content_has_refusal = False
                if content is not None:
                    content_has_refusal = any(phrase in content for phrase in refusal_phrases)

                message_has_refusal = hasattr(response.choices[0].message, 'refusal') and response.choices[0].message.refusal

                if content_has_refusal or message_has_refusal or content is None:
                    safe_movie = self._safe_encode_string(movie_title)
                    safe_person = self._safe_encode_string(person_name)
                    logger.warning(f"API refused to answer for {safe_movie} - {safe_person}")
                    response_metadata["error"] = "API refused to answer"
                    response_metadata["extracted_data"] = {
                        "chinese_name": person_name,
                        "english_name": None,
                        "birth_date": None,
                        "education_institution": None,
                        "has_university_education": None,
                        "profession": self.field_to_process,
                        "birth_country": None,
                        "birth_city": None,
                        "gender": None,
                        "nationality": None,
                        "note": "API refused to answer this query"
                    }
                    
                    # 记录被拒绝的请求，用于后续重试
                    person_key = f"{movie_title}_{person_name}"
                    self.refused_requests[person_key] = {
                        "movie_title": movie_title,
                        "person_name": person_name,
                        "role": self.field_to_process,
                        "timestamp": datetime.now().isoformat(),
                        "refusal_content": content,
                        "retry_count": self.refused_requests.get(person_key, {}).get("retry_count", 0) + 1
                    }
                    self._save_refused_requests()
                    
                    return response_metadata
                
                # 尝试解析JSON响应
                if content is not None and content.strip():
                    try:
                        extracted_data = json.loads(content)
                        response_metadata["extracted_data"] = extracted_data
                        return response_metadata
                    except json.JSONDecodeError as e:
                        safe_error = self._safe_encode_string(str(e))
                        logger.warning(f"Error parsing JSON response: {safe_error}")
                        response_metadata["error"] = f'JSON parsing error: {str(e)}'
                else:
                    # content为None或空字符串
                    logger.warning(f"Empty or None content received from API")
                    response_metadata["error"] = 'Empty or None content received from API'

                # 对于无法解析的JSON或空内容，提供基本的默认数据
                response_metadata["extracted_data"] = {
                    "chinese_name": person_name,
                    "english_name": None,
                    "birth_date": None,
                    "education_institution": None,
                    "has_university_education": None,
                    "profession": self.field_to_process,
                    "birth_country": None,
                    "birth_city": None,
                    "gender": None,
                    "nationality": None,
                    "note": "Failed to parse API response or empty content"
                }
                return response_metadata
                    
            logger.error(f"Invalid API response format: {response}")
            response_metadata["error"] = 'Invalid API response format'
            return response_metadata
            
        except Exception as e:
            error_str = str(e)
            safe_error = self._safe_encode_string(error_str)
            logger.error(f"API request error: {safe_error}")
            response_metadata["error"] = f'API request error: {error_str}'
            
            # 检测429错误（速率限制）
            if "429" in error_str or "RPD exceeded" in error_str:
                safe_client_idx = self._safe_encode_string(str(client_idx))
                logger.warning(f"API key {safe_client_idx} 触发了速率限制，将被禁用")
                self.disabled_api_keys.add(client_idx)
                # 记录被禁用的API密钥
                errors_path = self.output_dir / "disabled_api_keys.json"
                with open(errors_path, 'w', encoding='utf-8') as f:
                    json.dump({
                        "disabled_keys": list(self.disabled_api_keys),
                        "timestamp": datetime.now().isoformat(),
                        "reason": error_str
                    }, f, ensure_ascii=False, indent=2)
                
                # 如果还有其他可用的API密钥，递归调用自身重试
                available_clients = [i for i in range(len(self.clients)) if i not in self.disabled_api_keys]
                if available_clients:
                    safe_count = self._safe_encode_string(str(len(available_clients)))
                    logger.info(f"重试使用其他API密钥: {safe_count}个可用密钥")
                    await asyncio.sleep(2)  # 短暂延迟后重试
                    return await self._query_infini_api(movie_title, person_name)
            
            return response_metadata

    async def _process_batch(self, batch: List[int]) -> None:
        """处理一批电影数据
        
        Args:
            batch: 要处理的电影ID列表
        """
        # 在处理批次前刷新结果数据，避免重复处理
        self._refresh_results()
        
        logger.info(f"Processing batch of {len(batch)} items")
        tasks = []
        available_clients = []  # 初始化变量
        
        # 获取所有可用的API密钥索引
        available_clients = [i for i in range(len(self.clients)) if i not in self.disabled_api_keys]
        if not available_clients:
            logger.error("所有API密钥都已被禁用，无法继续处理")
            self._save_and_exit_on_all_disabled()
            return
            
        for movie_id in batch:
            if movie_id in self.df.index:
                movie_row = self.df.loc[movie_id]
                # 使用动态标题字段
                try:
                    movie_title = movie_row[self.get_title_field()]
                except KeyError:
                    logger.warning(f"标题字段 '{self.get_title_field()}' 不存在，跳过记录 {movie_id}")
                    continue

                # 使用动态字段映射
                field_name = self.get_field_name(self.field_to_process)
                try:
                    field_value = movie_row[field_name]
                except KeyError:
                    logger.warning(f"字段 '{field_name}' 不存在于数据类型 '{self.data_type}' 中，跳过记录 {movie_id}")
                    continue
                
                if isinstance(field_value, pd.Series):
                    field_value = field_value.iloc[0]
                    
                if pd.isna(field_value) or (isinstance(field_value, str) and field_value.strip() == ''):
                    continue
                    
                persons = str(field_value).split('/')
                for person in persons:
                    person = person.strip()
                    if person:
                        # 检查是否需要处理这个记录（增量处理）
                        if self._should_process_record(movie_id, person):
                            tasks.append((movie_id, movie_title, person))
                        else:
                            logger.debug(f"跳过已处理的记录: {movie_id}_{person}_{self.field_to_process}")
        
        # 检查任务是否为空
        if not tasks:
            logger.warning("No tasks to process in this batch")
            return
            
        total_tasks = len(tasks)
        logger.info(f"Created {total_tasks} tasks for processing")
        
        # 将任务平均分配给可用的API密钥
        num_clients = len(available_clients)
        tasks_per_client = [[] for _ in range(num_clients)]
        
        # 平均分配任务给每个可用的API密钥
        for i, task in enumerate(tasks):
            client_idx = i % num_clients
            tasks_per_client[client_idx].append(task)
            
        # 创建每个API密钥的处理协程
        client_tasks = []
        for i, client_idx in enumerate(available_clients):
            if tasks_per_client[i]:  # 如果该客户端有分配到任务
                client_tasks.append(self._process_client_tasks(client_idx, tasks_per_client[i]))
                
        # 并行执行所有API密钥的处理任务
        completed_count = 0
        results = await asyncio.gather(*client_tasks, return_exceptions=True)
        
        # 处理结果
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"Client task error: {str(result)}")
                continue
                
            client_completed_count, processed_ids = result
            completed_count += client_completed_count
            
            # 更新已处理ID列表
            for movie_id in processed_ids:
                if movie_id not in self.checkpoint["processed_ids"]:
                    self.checkpoint["processed_ids"].append(movie_id)
        
        logger.info(f"Batch completed. Processed {completed_count}/{total_tasks} tasks")
        
        # 保存进度
        self._save_checkpoint()
        self._save_results()
        self._save_errors()
        
        # 如果启用了重试失败项目，并且有错误，则立即重试
        if self.retry_failed and self.errors:
            logger.info("发现失败项目，开始重试...")
            await self._retry_failed_items()

    def _refresh_results(self) -> None:
        """从文件刷新结果数据，确保不会重复处理已有数据

        这在多个处理实例并行运行时特别有用，可以避免重复工作
        """
        results_path = self.output_dir / self.results_file
        if results_path.exists():
            try:
                with open(results_path, 'r', encoding='utf-8') as f:
                    latest_results = json.load(f)
                # 更新当前结果，但不覆盖已有的结果
                # 这样确保已经处理过的新结果不会丢失
                for key, value in latest_results.items():
                    if key not in self.results:
                        self.results[key] = value
                logger.debug(f"已刷新结果数据，当前结果总数: {len(self.results)}")
            except Exception as e:
                logger.error(f"刷新结果数据时出错: {str(e)}")
                # 出错时不更新结果，避免数据损坏
        
    async def _process_client_tasks(self, client_idx: int, tasks: List[Tuple[int, str, str]]) -> Tuple[int, List[int]]:
        """使用指定的API客户端处理一组任务
        
        Args:
            client_idx: API客户端的索引
            tasks: 任务列表，每个任务是 (movie_id, movie_title, person_name) 的元组
            
        Returns:
            完成的任务数量和已处理的电影ID列表
        """
        completed_count = 0
        processed_ids = []
        
        logger.info(f"API密钥 {client_idx+1}/{len(self.clients)} 开始处理 {len(tasks)} 个任务")
        
        # 创建带有详细信息的进度条
        with tqdm(tasks, desc=f"API密钥 {client_idx+1}/{len(self.clients)}", 
                 leave=False, position=client_idx+1) as pbar:
            for movie_id, movie_title, person_name in pbar:
                person_key = f"{movie_id}_{person_name}"
                
                # 更新进度条显示当前处理的内容
                pbar.set_postfix({
                    '电影': movie_title[:20] + '...' if len(movie_title) > 20 else movie_title,
                    '人员': person_name[:15] + '...' if len(person_name) > 15 else person_name
                })
                
                if person_key in self.results:
                    logger.debug(f"Skipping {person_key} - already processed")
                    if movie_id not in processed_ids:
                        processed_ids.append(movie_id)
                    completed_count += 1
                    continue
                    
                # 每个任务之间短暂延迟，避免单个API密钥过载
                await asyncio.sleep(1)
                api_response = await self._query_infini_api(movie_title, person_name, client_idx)
                
                if 'error' in api_response and '403' in str(api_response['error']):
                    logger.warning(f"Received 403 error for {person_key}. Taking a longer break...")
                    await asyncio.sleep(10)
                    logger.info(f"Retrying after break for {person_key}")
                    api_response = await self._query_infini_api(movie_title, person_name, client_idx)
                    
                self.results[person_key] = api_response
                
                if 'error' in api_response:
                    self.errors[person_key] = {
                        'movie_id': movie_id,
                        'movie_title': movie_title,
                        'person_name': person_name,
                        'error': api_response['error'],
                        'request_time': api_response.get("request_time"),
                        'response_time_seconds': api_response.get("response_time_seconds"),
                        'http_status_code': api_response.get("http_status_code"),
                        'timestamp': datetime.now().isoformat()
                    }
                    logger.warning(f"Error processing {person_key}: {api_response['error']}")
                    
                    if '403' in str(api_response['error']):
                        logger.error(f"Still receiving 403 errors for API key {client_idx+1} after retry.")
                        break
                
                if movie_id not in processed_ids:
                    processed_ids.append(movie_id)
                    
                completed_count += 1
                if completed_count % 800 == 0:
                    # 每处理80个项目保存一次进度
                    self._save_results()
                    self._save_errors()
                    await asyncio.sleep(1)  # 每80个请求后额外休息0.5秒
        
        logger.info(f"API密钥 {client_idx+1}/{len(self.clients)} 完成处理，成功处理 {completed_count}/{len(tasks)} 个任务")
        return completed_count, processed_ids

    async def process_data(self) -> None:
        if not self.data_loaded:
            self.load_data()
            
        # 在开始处理前刷新结果数据，避免重复处理
        self._refresh_results()
        logger.info(f"已加载现有结果数据，当前结果总数: {len(self.results)}")
        
        # 根据已有结果更新检查点进度
        self.update_checkpoint_from_results()
            
        # 如果设置了export_only，则只导出当前结果
        if self.export_only:
            logger.info("export_only模式：只导出当前处理结果，不进行新的API请求")
            self.export_results(export_format='csv')
            return
            
        if "current_field" in self.checkpoint and self.checkpoint["current_field"] != self.field_to_process:
            logger.info(f"Switching from {self.checkpoint['current_field']} to {self.field_to_process}")
            self.checkpoint["current_field"] = self.field_to_process
            self.checkpoint["last_processed_index"] = 0
            self.checkpoint["processed_ids"] = []
            self._save_checkpoint()
        
        # 先尝试处理之前被拒绝的请求
        if self.retry_refused and self.refused_requests:
            logger.info("开始重试之前被拒绝的请求...")
            await self._retry_refused_requests()
        elif self.refused_requests:
            logger.info(f"发现 {len(self.refused_requests)} 个被拒绝的请求，但由于 retry_refused=False，将跳过重试")
            # 显示被拒绝的请求详情
            #for person_key, refused_item in self.refused_requests.items():
            #    logger.info(f"被拒绝的请求: {person_key} - {refused_item['movie_title']} - {refused_item['person_name']}")
        
        # 筛选需要处理的记录（增量处理）
        all_ids = self.df.index.tolist()
        unprocessed_ids = []

        logger.info(f"开始筛选需要处理的记录...")
        logger.info(f"当前结果文件包含 {len(self.results)} 条记录")

        # 调试：检查前几个记录的处理状态
        debug_count = 0
        for idx in all_ids[:10]:  # 只检查前10个记录用于调试
            row = self.df.loc[idx]
            movie_id = row['ID']
            field_name = self.get_field_name(self.field_to_process)
            if field_name in row and pd.notna(row[field_name]):
                field_value = row[field_name]
                if isinstance(field_value, pd.Series):
                    field_value = field_value.iloc[0]
                if not pd.isna(field_value) and str(field_value).strip():
                    persons = str(field_value).split('/')
                    for person in persons:
                        person = person.strip()
                        if person:
                            result_key = f"{movie_id}_{person}"
                            should_process = self._should_process_record(movie_id, person)
                            logger.info(f"调试 - 电影ID:{movie_id}, 人员:{person}, 键:{result_key}, 在结果中:{result_key in self.results}, 应处理:{should_process}")
                            debug_count += 1
                            if debug_count >= 5:  # 只调试前5个人员
                                break
                if debug_count >= 5:
                    break

        for idx in all_ids:
            row = self.df.loc[idx]
            movie_id = row['ID']  # 修复：使用正确的列名

            # 获取对应角色的人员列表
            field_name = self.get_field_name(self.field_to_process)
            if field_name in row:
                field_value = row[field_name]
                if isinstance(field_value, pd.Series):
                    field_value = field_value.iloc[0]

                if pd.isna(field_value) or (isinstance(field_value, str) and field_value.strip() == ''):
                    continue

                persons = str(field_value).split('/')  # 修复：正确分割人员列表

                # 检查是否有未处理的人员
                has_unprocessed = False
                for person in persons:
                    person = person.strip()
                    if person and self._should_process_record(movie_id, person):
                        has_unprocessed = True
                        break

                if has_unprocessed:
                    unprocessed_ids.append(idx)

        total_ids = len(unprocessed_ids)
        start_index = 0  # 重新开始，因为我们已经筛选了需要处理的记录

        logger.info(f"原始记录总数: {len(all_ids)}")
        logger.info(f"需要处理的记录数: {total_ids}")
        logger.info(f"已处理数据库记录数: {len(self.processed_database)}")

        # 设置进度跟踪的总数
        self.total_count = total_ids
        self.processed_count = 0

        if total_ids == 0:
            logger.info("所有记录都已处理完成，无需进行新的处理")
            return

        # 创建总体进度条
        with tqdm(total=total_ids, initial=start_index, desc=f"总体进度 - 处理{self.field_to_process}", unit="电影") as overall_pbar:
            for i in range(start_index, total_ids, self.batch_size):
                batch = unprocessed_ids[i:min(i + self.batch_size, total_ids)]
                batch_num = i//self.batch_size + 1
                total_batches = (total_ids + self.batch_size - 1)//self.batch_size
                
                logger.info(f"Processing batch {batch_num}/{total_batches}")

                # 检查是否应该暂停
                while self.paused and not self.should_stop:
                    logger.info("处理已暂停，等待继续...")
                    await asyncio.sleep(1)
                
                # 检查是否应该停止
                if self.should_stop:
                    logger.info("处理已手动停止")
                    break

                # 在处理批次前检查API密钥状态
                if self._all_api_keys_disabled():
                    self._save_and_exit_on_all_disabled()

                await self._process_batch(batch)

                # 在处理批次后再次检查API密钥状态
                if self._all_api_keys_disabled():
                    self._save_and_exit_on_all_disabled()

                # 更新检查点
                self.checkpoint["last_processed_index"] = min(i + self.batch_size, total_ids)
                self._save_checkpoint()
                
                # 更新总体进度条
                processed = min(i + self.batch_size, total_ids)
                overall_pbar.update(len(batch))
                overall_pbar.set_postfix({
                    '批次': f'{batch_num}/{total_batches}',
                    '完成率': f'{processed/total_ids*100:.1f}%',
                    '已处理': f'{processed}/{total_ids}'
                })
                
                # 更新进度跟踪属性
                self.processed_count = processed
                if processed > 0:
                    self.success_rate = (len(self.results) / processed) * 100
                self.error_count = len(self.error_log)
                
                logger.info(f"Progress: {processed}/{total_ids} ({processed/total_ids*100:.2f}%)")

                # 检查是否需要更新数据库
                self._update_processed_database_from_results()
                if self.database_update_counter >= self.database_update_interval:
                    logger.info(f"已处理 {self.database_update_counter} 条记录，更新数据库文件...")
                    self._save_processed_database()
                    self.database_update_counter = 0

                # 如果启用了导出当前结果，则导出
                if self.export_current_results:
                    self.export_current_processing_results()
                
        # 处理完成后，最终更新一次数据库
        logger.info("处理完成，进行最终数据库更新...")
        self._update_processed_database_from_results()
        self._save_processed_database()

        # 根据retry_failed参数决定是否重试失败的项目
        if self.retry_failed:
            await self._retry_failed_items()
        elif self.errors:
            logger.info(f"发现 {len(self.errors)} 个失败项目，但由于 retry_failed=False，将跳过重试")

    async def _retry_refused_requests(self) -> None:
        """重试之前被API拒绝回答的请求，采用并行方式处理"""
        if not self.retry_refused:
            logger.info("retry_refused=False，跳过重试被拒绝的请求")
            return
            
        if not self.refused_requests:
            logger.info("没有需要重试的被拒绝请求")
            return
            
        logger.info(f"找到 {len(self.refused_requests)} 个被拒绝的请求，开始并行重试")
        retry_keys = list(self.refused_requests.keys())
        max_retry_count = 3  # 最大重试次数
        
        # 筛选出未达到最大重试次数的请求
        retry_tasks = []
        processing_requests = set()  # 跟踪正在处理的请求
        
        for person_key in retry_keys:
            refused_item = self.refused_requests[person_key]
            retry_count = refused_item.get('retry_count', 0)
            
            # 检查是否已经在结果中
            if person_key in self.results:
                logger.info(f"请求 {person_key} 已经在结果中，跳过重试")
                continue
                
            # 检查是否正在处理中
            if person_key in processing_requests:
                logger.info(f"请求 {person_key} 正在处理中，跳过重试")
                continue
            
            if retry_count < max_retry_count:
                movie_title = refused_item['movie_title']
                person_name = refused_item['person_name']
                retry_tasks.append((person_key, movie_title, person_name))
                processing_requests.add(person_key)
            else:
                logger.info(f"已达到最大重试次数({max_retry_count})，跳过 {person_key}")
                
        if not retry_tasks:
            logger.info("没有需要重试的请求")
            return
            
        logger.info(f"准备并行重试 {len(retry_tasks)} 个请求")
        
        # 获取可用API密钥
        available_clients = [i for i in range(len(self.clients)) if i not in self.disabled_api_keys]
        if not available_clients:
            logger.error("所有API密钥都已被禁用，无法继续重试")
            self._save_and_exit_on_all_disabled()
            return
            
        # 将任务平均分配给可用API密钥
        num_clients = len(available_clients)
        tasks_per_client = [[] for _ in range(num_clients)]
        
        for i, task in enumerate(retry_tasks):
            client_idx = i % num_clients
            tasks_per_client[client_idx].append(task)
            
        # 创建每个API密钥的处理协程
        client_tasks = []
        for i, client_idx in enumerate(available_clients):
            if tasks_per_client[i]:  # 如果该客户端有分配到任务
                client_tasks.append(self._retry_client_tasks(client_idx, tasks_per_client[i]))
                
        # 并行执行所有API密钥的处理任务
        results = await asyncio.gather(*client_tasks, return_exceptions=True)
        
        # 处理结果
        total_success = 0
        total_processed = 0
        
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"重试任务错误: {str(result)}")
                continue
                
            success_count, processed_count = result
            total_success += success_count
            total_processed += processed_count
            
        # 保存进度
        self._save_results()
        self._save_refused_requests()

        # 重试完成后更新数据库
        logger.info("重试被拒绝请求完成，更新数据库...")
        self._update_processed_database_from_results()
        self._save_processed_database()

        logger.info(f"重试完成，成功处理 {total_success}/{total_processed} 个请求")
        
    async def _retry_client_tasks(self, client_idx: int, tasks: List[Tuple[str, str, str]]) -> Tuple[int, int]:
        """使用指定的API客户端高并发处理重试任务
        
        Args:
            client_idx: API客户端的索引
            tasks: 任务列表，每个任务是 (person_key, movie_title, person_name) 的元组
            
        Returns:
            成功重试的数量和处理的总数量
        """
        successful_retries = 0
        processed_count = 0
        
        logger.info(f"API密钥 {client_idx+1}/{len(self.clients)} 开始处理 {len(tasks)} 个重试任务")
        
        # 设置并发和速率限制参数
        concurrency = 1  # 每个API密钥同时处理的请求数
        batch_size = 10  # 每次处理的批次大小
        
        # 创建进度条
        with tqdm(total=len(tasks), desc=f"API密钥 {client_idx+1}/{len(self.clients)} 重试", 
                 leave=False, position=client_idx+1) as pbar:
            
            # 将任务分成批次处理
            for i in range(0, len(tasks), batch_size):
                batch = tasks[i:i+batch_size]
                current_tasks = []
                
                for person_key, movie_title, person_name in batch:
                    # 构建异步任务，但不立即执行
                    task = self._process_single_retry_task(client_idx, person_key, movie_title, person_name)
                    current_tasks.append(task)
                
                # 控制并发，每concurrency个并行请求
                for j in range(0, len(current_tasks), concurrency):
                    sub_batch = current_tasks[j:j+concurrency]

                    # 添加调试信息和错误处理
                    if not isinstance(sub_batch, list):
                        logger.error(f"sub_batch 不是列表类型: {type(sub_batch)}, 值: {sub_batch}")
                        continue

                    # 并行执行这一小批次的请求
                    results = await asyncio.gather(*sub_batch, return_exceptions=True)

                    # 处理结果
                    for result in results:
                        processed_count += 1
                        if isinstance(result, Exception):
                            logger.error(f"重试任务异常: {str(result)}")
                            continue

                        if result:  # 如果重试成功
                            successful_retries += 1

                    # 更新进度条 - 添加错误处理
                    try:
                        batch_size_update = len(sub_batch)
                        if not isinstance(batch_size_update, int):
                            logger.error(f"len(sub_batch) 返回非整数: {type(batch_size_update)}, 值: {batch_size_update}")
                            batch_size_update = 1  # 使用默认值
                        pbar.update(batch_size_update)
                    except Exception as e:
                        logger.error(f"更新进度条时出错: {str(e)}, sub_batch类型: {type(sub_batch)}, 值: {sub_batch}")
                        pbar.update(1)  # 使用默认值继续
                    pbar.set_postfix({
                        '成功率': f'{successful_retries}/{processed_count}',
                        '进度': f'{processed_count}/{len(tasks)}'
                    })
                
                # 每批次后保存进度
                if processed_count % batch_size == 0:
                    self._save_results()
                    self._save_refused_requests()
        
        logger.info(f"API密钥 {client_idx+1}/{len(self.clients)} 完成重试，成功 {successful_retries}/{len(tasks)} 个任务")
        return successful_retries, processed_count
        
    async def _process_single_retry_task(self, client_idx: int, person_key: str, movie_title: str, person_name: str) -> bool:
        """处理单个重试任务
        
        Returns:
            布尔值，指示重试是否成功
        """
        try:
            api_response = await self._query_infini_api(movie_title, person_name, client_idx)
            
            if 'error' not in api_response or api_response.get('error') != "API refused to answer":
                # 使用安全的字符串格式化，避免编码问题
                safe_key = self._safe_encode_string(person_key)
                logger.info(f"重试成功: {safe_key}")
                
                # 更新结果
                movie_id = None
                try:
                    parts = person_key.split('_', 1)
                    if len(parts) == 2:
                        movie_id = int(parts[0])
                except Exception:
                    # 如果无法从person_key提取movie_id，则尝试在数据集中查找
                    title_field = self.get_title_field()
                    for idx, row in self.df.iterrows():
                        if title_field in row and row[title_field] == movie_title:
                            movie_id = idx
                            break
                
                if movie_id is not None:
                    result_key = f"{movie_id}_{person_name}"
                    self.results[result_key] = api_response
                    # 从refused_requests中移除
                    if person_key in self.refused_requests:
                        del self.refused_requests[person_key]
                    return True
            else:
                # 更新重试次数
                if person_key in self.refused_requests:
                    retry_count = self.refused_requests[person_key].get('retry_count', 0)
                    self.refused_requests[person_key]['retry_count'] = retry_count + 1
                    self.refused_requests[person_key]['last_retry'] = datetime.now().isoformat()
                return False
                
        except Exception as e:
            # 使用安全的字符串格式化，避免编码问题
            safe_error = self._safe_encode_string(str(e))
            logger.error(f"处理重试任务时出错: {safe_error}")
            return False
        
        return False

    async def _retry_failed_items(self) -> None:
        """采用并行方式重试失败的项目"""
        if not self.errors:
            logger.info("无需重试失败项目")
            return
            
        logger.info(f"找到 {len(self.errors)} 个失败项目。开始并行重试...")
        error_keys = list(self.errors.keys())
        
        # 准备重试任务
        retry_tasks = []
        for person_key in error_keys:
            error_item = self.errors[person_key]
            movie_id = error_item['movie_id']
            movie_title = error_item['movie_title']
            person_name = error_item['person_name']
            retry_tasks.append((person_key, movie_id, movie_title, person_name))
            
        # 获取可用API密钥
        available_clients = [i for i in range(len(self.clients)) if i not in self.disabled_api_keys]
        
        # 如果没有可用的API密钥，尝试重置
        if not available_clients:
            logger.warning("所有API密钥都已被禁用，尝试重置API密钥...")
            self._check_and_reset_api_keys()
            available_clients = [i for i in range(len(self.clients)) if i not in self.disabled_api_keys]
            
        # 如果重置后仍然没有可用的API密钥，则返回
        if not available_clients:
            logger.error("重置API密钥后仍然没有可用的密钥，无法继续重试")
            self._save_and_exit_on_all_disabled()
            return
            
        # 将任务平均分配给可用API密钥
        num_clients = len(available_clients)
        tasks_per_client = [[] for _ in range(num_clients)]
        
        for i, task in enumerate(retry_tasks):
            client_idx = i % num_clients
            tasks_per_client[client_idx].append(task)
            
        # 创建每个API密钥的处理协程
        client_tasks = []
        for i, client_idx in enumerate(available_clients):
            if tasks_per_client[i]:  # 如果该客户端有分配到任务
                client_tasks.append(self._retry_error_client_tasks(client_idx, tasks_per_client[i]))
                
        # 并行执行所有API密钥的处理任务
        results = await asyncio.gather(*client_tasks, return_exceptions=True)
        
        # 处理结果
        total_success = 0
        total_processed = 0
        
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"重试任务错误: {str(result)}")
                continue
                
            success_count, processed_count = result
            total_success += success_count
            total_processed += processed_count
            
        # 保存进度
        self._save_results()
        self._save_errors()
        
        logger.info(f"并行重试失败项目完成。成功重试 {total_success}/{total_processed} 个请求。")
        
    async def _retry_error_client_tasks(self, client_idx: int, tasks: List[Tuple[str, Any, str, str]]) -> Tuple[int, int]:
        """使用指定的API客户端高并发处理失败项目的重试
        
        Args:
            client_idx: API客户端的索引
            tasks: 任务列表，每个任务是 (person_key, movie_id, movie_title, person_name) 的元组
            
        Returns:
            成功重试的数量和处理的总数量
        """
        successful_retries = 0
        processed_count = 0
        
        logger.info(f"API密钥 {client_idx+1}/{len(self.clients)} 开始处理 {len(tasks)} 个失败项目")
        
        # 设置并发和速率限制参数
        concurrency = 5  # 每个API密钥同时处理的请求数
        batch_size = 10  # 每次处理的批次大小
        
        # 创建进度条
        with tqdm(total=len(tasks), desc=f"API密钥 {client_idx+1}/{len(self.clients)} 重试错误", 
                 leave=False, position=client_idx+1) as pbar:
            
            # 将任务分成批次处理
            for i in range(0, len(tasks), batch_size):
                batch = tasks[i:i+batch_size]
                current_tasks = []
                
                for person_key, movie_id, movie_title, person_name in batch:
                    # 构建异步任务，但不立即执行
                    task = self._process_single_error_task(client_idx, person_key, movie_title, person_name)
                    current_tasks.append(task)
                
                # 控制并发，每concurrency个并行请求
                for j in range(0, len(current_tasks), concurrency):
                    sub_batch = current_tasks[j:j+concurrency]

                    # 添加调试信息和错误处理
                    if not isinstance(sub_batch, list):
                        logger.error(f"sub_batch 不是列表类型: {type(sub_batch)}, 值: {sub_batch}")
                        continue

                    # 并行执行这一小批次的请求
                    results = await asyncio.gather(*sub_batch, return_exceptions=True)

                    # 处理结果
                    for result in results:
                        processed_count += 1
                        if isinstance(result, Exception):
                            logger.error(f"重试错误任务异常: {str(result)}")
                            continue

                        if result:  # 如果重试成功
                            successful_retries += 1

                    # 更新进度条 - 添加错误处理
                    try:
                        batch_size_update = len(sub_batch)
                        if not isinstance(batch_size_update, int):
                            logger.error(f"len(sub_batch) 返回非整数: {type(batch_size_update)}, 值: {batch_size_update}")
                            batch_size_update = 1  # 使用默认值
                        pbar.update(batch_size_update)
                    except Exception as e:
                        logger.error(f"更新进度条时出错: {str(e)}, sub_batch类型: {type(sub_batch)}, 值: {sub_batch}")
                        pbar.update(1)  # 使用默认值继续
                    pbar.set_postfix({
                        '成功率': f'{successful_retries}/{processed_count}',
                        '进度': f'{processed_count}/{len(tasks)}'
                    })
                
                # 每批次后保存进度
                if processed_count % batch_size == 0:
                    self._save_results()
        
        logger.info(f"API密钥 {client_idx+1}/{len(self.clients)} 完成失败项目重试，成功 {successful_retries}/{len(tasks)} 个任务")
        return successful_retries, processed_count
        
    async def _process_single_error_task(self, client_idx: int, person_key: str, movie_title: str, person_name: str) -> bool:
        """处理单个错误重试任务
        
        Returns:
            布尔值，指示重试是否成功
        """
        try:
            api_response = await self._query_infini_api(movie_title, person_name, client_idx)
            
            if 'error' not in api_response:
                # 使用安全的字符串格式化，避免编码问题
                safe_key = self._safe_encode_string(person_key)
                logger.info(f"重试成功: {safe_key}")
                
                # 尝试解析person_key，提取movie_id
                movie_id = None
                try:
                    parts = person_key.split('_', 1)
                    if len(parts) == 2:
                        movie_id = int(parts[0])
                except Exception:
                    # 如果无法从person_key提取movie_id，则尝试在数据集中查找
                    title_field = self.get_title_field()
                    for idx, row in self.df.iterrows():
                        if title_field in row and row[title_field] == movie_title:
                            movie_id = idx
                            break
                
                # 确定结果键
                result_key = person_key
                if movie_id is not None and '_' not in person_key:
                    result_key = f"{movie_id}_{person_name}"
                
                # 更新结果
                self.results[result_key] = api_response
                return True
            else:
                # 使用安全的字符串格式化，避免编码问题
                safe_key = self._safe_encode_string(person_key)
                safe_error = self._safe_encode_string(api_response['error'])
                logger.warning(f"API错误重试失败: {safe_key} - {safe_error}")
                return False
                
        except Exception as e:
            # 使用安全的字符串格式化，避免编码问题
            safe_error = self._safe_encode_string(str(e))
            logger.error(f"处理API错误重试任务时出错: {safe_error}")
            return False
            
    def _safe_encode_string(self, text: str) -> str:
        """安全地编码字符串，避免编码问题
        
        Args:
            text: 原始字符串
            
        Returns:
            安全处理后的字符串
        """
        if not isinstance(text, str):
            text = str(text)
            
        try:
            # 对特殊字符进行处理，使其能在任何编码环境中安全地显示
            return text.encode('utf-8', errors='backslashreplace').decode('utf-8')
        except Exception:
            # 如果出现任何错误，返回简单的占位符
            return "[无法显示的字符]"

    def export_results(self, export_format="csv") -> str:
        self._save_results()
        self.output_dir.mkdir(parents=True, exist_ok=True)
        export_data = []

        for person_key, result in self.results.items():
            if 'error' in result and 'extracted_data' not in result:
                continue

            movie_id, person_name = person_key.split('_', 1)
            movie_title = ""

            # 尝试将movie_id转换为整数，如果失败则跳过（处理使用movie_title作为键的情况）
            try:
                movie_id_int = int(movie_id)
                if movie_id_int in self.df.index:
                    title_field = self.get_title_field()
                    movie_title = self.df.loc[movie_id_int][title_field]
            except ValueError:
                # 如果movie_id不是数字（比如是movie_title），则直接使用它作为movie_title
                movie_title = movie_id
                movie_id = "unknown"  # 设置一个默认值
                
            row = {
                'movie_id': movie_id,
                'movie_title': movie_title,
                'person_name': person_name,
                'role': self.field_to_process,
                'request_time': result.get('request_time'),
                'response_timestamp': result.get('response_timestamp'),
                'response_time_seconds': result.get('response_time_seconds'),
                'model': result.get('model'),
                'temperature': result.get('temperature'),
                'http_status_code': result.get('http_status_code')
            }
            
            if 'extracted_data' in result and result['extracted_data']:
                extracted_data = result['extracted_data']
                if isinstance(extracted_data, dict):
                    for key, value in extracted_data.items():
                        row[key] = value
                elif isinstance(extracted_data, list):
                    # 如果是列表，将其转换为字符串存储
                    row['extracted_data'] = json.dumps(extracted_data, ensure_ascii=False, default=str)
                else:
                    # 其他类型直接存储
                    row['extracted_data'] = str(extracted_data)
                    
            if 'raw_response' in result:
                row['raw_response_json'] = json.dumps(result['raw_response'], ensure_ascii=False, default=str)
            if 'message_content' in result:
                row['message_content'] = result['message_content']
            if 'error' in result:
                row['error'] = result['error']
                
            export_data.append(row)
            
        export_df = pd.DataFrame(export_data)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        pkl_output_path = self.output_dir / f"infini_{self.field_to_process}_results_{timestamp}.pkl"
        export_df.to_pickle(pkl_output_path)
        logger.info(f"Results also exported to pickle: {pkl_output_path}")
        
        if export_format.lower() == 'csv':
            output_path = self.output_dir / f"infini_{self.field_to_process}_results_{timestamp}.csv"
            output_path.parent.mkdir(parents=True, exist_ok=True)
            export_df.to_csv(output_path, index=False, encoding='utf-8-sig')
            
            raw_output_path = self.output_dir / f"infini_{self.field_to_process}_raw_results_{timestamp}.json"
            with open(raw_output_path, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2, default=str)
            logger.info(f"Raw results also exported to {raw_output_path}")
        else:
            output_path = self.output_dir / f"infini_{self.field_to_process}_results_{timestamp}.json"
            output_path.parent.mkdir(parents=True, exist_ok=True)
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2, default=str)
                
        logger.info(f"Exported {len(export_data)} results to {output_path}")
        return str(output_path)

    def export_errors(self) -> str:
        if not self.errors:
            logger.info("No errors to export")
            return ""
            
        error_df = pd.DataFrame(list(self.errors.values()))
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = self.output_dir / f"infini_{self.field_to_process}_errors_{timestamp}.csv"
        error_df.to_csv(output_path, index=False, encoding='utf-8-sig')
        logger.info(f"Exported {len(self.errors)} errors to {output_path}")
        return str(output_path)

    def extract_api_errors(self, export_to_file: bool = True) -> Dict:
        """
        从处理结果中提取API请求错误，并将它们保存到单独的文件中以便后续重试。
        
        Args:
            export_to_file: 是否将错误导出到文件，默认为True
            
        Returns:
            包含所有API错误的字典
        """
        logger.info("开始检查API请求错误...")
        
        # 确保结果已加载
        if not self.results:
            self._load_results()
            
        api_errors = {}
        error_categories = {
            'rate_limit_errors': 0,
            'timeout_errors': 0,
            'connection_errors': 0, 
            'json_parse_errors': 0,
            'other_errors': 0
        }
        
        # 遍历所有结果，检查错误
        for person_key, result in self.results.items():
            if 'error' in result:
                error_msg = result.get('error', '')
                movie_title = result.get('movie_title', '')
                person_name = result.get('person_name', '')
                
                # 尝试从person_key中提取movie_id和person_name（如果没有在结果中直接提供）
                if not movie_title or not person_name:
                    try:
                        parts = person_key.split('_', 1)
                        if len(parts) == 2:
                            movie_id, extracted_name = parts
                            movie_id = int(movie_id)
                            if movie_id in self.df.index:
                                title_field = self.get_title_field()
                                movie_title = movie_title or self.df.loc[movie_id][title_field]
                                person_name = person_name or extracted_name
                    except Exception as e:
                        safe_key = self._safe_encode_string(person_key)
                        safe_error = self._safe_encode_string(str(e))
                        logger.warning(f"无法从person_key '{safe_key}'提取信息: {safe_error}")
                
                # 对错误进行分类
                error_type = 'other_errors'
                if '429' in error_msg:
                    if 'RPD exceeded' in error_msg:
                        error_type = 'rpd_limit_errors'  # RPD限制错误
                    else:
                        error_type = 'rate_limit_errors'  # HTTP 429错误
                elif 'timeout' in error_msg.lower() or 'time' in error_msg.lower():
                    error_type = 'timeout_errors'
                elif 'connect' in error_msg.lower() or 'connection' in error_msg.lower():
                    error_type = 'connection_errors'
                elif 'json' in error_msg.lower() or 'parse' in error_msg.lower():
                    error_type = 'json_parse_errors'
                
                # 递增错误计数
                error_categories[error_type] += 1
                
                # 将错误添加到集合中
                api_errors[person_key] = {
                    'person_key': person_key,
                    'movie_title': movie_title,
                    'person_name': person_name, 
                    'error': error_msg,
                    'error_type': error_type,
                    'original_request_time': result.get('request_time'),
                    'original_response_time': result.get('response_time_seconds'),
                    'extracted_data': result.get('extracted_data'),
                    'raw_response': result.get('raw_response'),
                    'retry_count': 0
                }
        
        # 输出错误统计信息
        total_errors = sum(error_categories.values())
        safe_total = self._safe_encode_string(str(total_errors))
        logger.info(f"发现 {safe_total} 个API请求错误：")
        for category, count in error_categories.items():
            if count > 0:
                safe_category = self._safe_encode_string(category)
                safe_count = self._safe_encode_string(str(count))
                logger.info(f"  - {safe_category}: {safe_count}")
        
        # 导出到文件
        if export_to_file and api_errors:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = self.output_dir / f"infini_api_errors_{timestamp}.json"
            self.output_dir.mkdir(parents=True, exist_ok=True)
            
            try:
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(api_errors, f, ensure_ascii=False, indent=2, default=str)
                safe_path = self._safe_encode_string(str(output_path))
                logger.info(f"API错误已保存到: {safe_path}")
            except Exception as e:
                safe_error = self._safe_encode_string(str(e))
                logger.error(f"保存API错误时出错: {safe_error}")
        
        return api_errors
        
    async def retry_api_errors(self, error_file: Optional[str] = None) -> Dict:
        """
        重试之前保存的API错误
        
        Args:
            error_file: API错误文件路径，如果为None，则自动查找最新的错误文件
            
        Returns:
            重试结果统计
        """
        # 找到最新的错误文件
        if error_file is None:
            error_files = list(self.output_dir.glob("infini_api_errors_*.json"))
            if not error_files:
                logger.warning("未找到API错误文件")
                return {"success": 0, "failed": 0}
            
            # 按创建时间排序
            error_files.sort(key=lambda x: x.stat().st_ctime, reverse=True)
            error_file = error_files[0]
        
        error_path = Path(error_file)
        if not error_path.exists():
            logger.error(f"错误文件不存在: {error_path}")
            return {"success": 0, "failed": 0}
        
        # 加载错误数据
        try:
            with open(error_path, 'r', encoding='utf-8') as f:
                api_errors = json.load(f)
            logger.info(f"已加载 {len(api_errors)} 个API错误来自: {error_path}")
        except Exception as e:
            logger.error(f"加载错误文件时出错: {str(e)}")
            return {"success": 0, "failed": 0}
        
        # 准备重试任务
        retry_tasks = []
        for person_key, error_data in api_errors.items():
            movie_title = error_data.get('movie_title')
            person_name = error_data.get('person_name')
            
            if movie_title and person_name:
                retry_tasks.append((person_key, movie_title, person_name))
        
        if not retry_tasks:
            logger.warning("没有可重试的任务")
            return {"success": 0, "failed": 0}
        
        logger.info(f"准备重试 {len(retry_tasks)} 个API错误")
        
        # 获取可用API密钥
        available_clients = [i for i in range(len(self.clients)) if i not in self.disabled_api_keys]
        if not available_clients:
            logger.error("所有API密钥都已被禁用，无法继续重试")
            self._save_and_exit_on_all_disabled()
            return {"success": 0, "failed": 0}
        
        # 将任务平均分配给可用API密钥
        num_clients = len(available_clients)
        tasks_per_client = [[] for _ in range(num_clients)]
        
        for i, task in enumerate(retry_tasks):
            client_idx = i % num_clients
            tasks_per_client[client_idx].append(task)
        
        # 创建每个API密钥的处理协程
        client_tasks = []
        for i, client_idx in enumerate(available_clients):
            if tasks_per_client[i]:  # 如果该客户端有分配到任务
                client_tasks.append(self._retry_api_error_tasks(client_idx, tasks_per_client[i], api_errors))
        
        # 并行执行所有API密钥的处理任务
        results = await asyncio.gather(*client_tasks, return_exceptions=True)
        
        # 处理结果
        total_success = 0
        total_failures = 0
        
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"重试API错误任务时发生异常: {str(result)}")
                continue
            
            success, failures = result
            total_success += success
            total_failures += failures
        
        # 保存进度和统计信息
        self._save_results()

        # 重试完成后更新数据库
        logger.info("重试完成，更新数据库...")
        self._update_processed_database_from_results()
        self._save_processed_database()

        # 更新和保存API错误文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        updated_errors_path = self.output_dir / f"infini_api_errors_updated_{timestamp}.json"
        remaining_errors = {k: v for k, v in api_errors.items() if k not in self.results or 'error' in self.results[k]}

        with open(updated_errors_path, 'w', encoding='utf-8') as f:
            json.dump(remaining_errors, f, ensure_ascii=False, indent=2, default=str)

        logger.info(f"重试API错误完成。成功: {total_success}, 失败: {total_failures}")
        logger.info(f"剩余 {len(remaining_errors)} 个错误，已保存到: {updated_errors_path}")
        
        return {
            "success": total_success,
            "failed": total_failures,
            "remaining": len(remaining_errors),
            "updated_errors_file": str(updated_errors_path)
        }
    
    async def _retry_api_error_tasks(self, client_idx: int, tasks: List[Tuple[str, str, str]], 
                                    api_errors: Dict) -> Tuple[int, int]:
        """使用指定的API客户端高并发处理API错误重试
        
        Args:
            client_idx: API客户端的索引
            tasks: 任务列表，每个任务是 (person_key, movie_title, person_name) 的元组
            api_errors: 原始API错误数据
            
        Returns:
            成功和失败的数量
        """
        successful_retries = 0
        failed_retries = 0
        
        safe_client_idx = self._safe_encode_string(str(client_idx+1))
        safe_task_count = self._safe_encode_string(str(len(tasks)))
        logger.info(f"API密钥 {safe_client_idx}/{len(self.clients)} 开始处理 {safe_task_count} 个API错误")
        
        # 设置并发和速率限制参数
        concurrency = 2  # 每个API密钥同时处理的请求数
        batch_size = 10  # 每次处理的批次大小
        
        # 创建进度条
        with tqdm(total=len(tasks), desc=f"API密钥 {client_idx+1}/{len(self.clients)} 重试API错误", 
                 leave=False, position=client_idx+1) as pbar:
            
            # 将任务分成批次处理
            for i in range(0, len(tasks), batch_size):
                batch = tasks[i:i+batch_size]
                current_tasks = []
                
                for person_key, movie_title, person_name in batch:
                    # 构建异步任务，但不立即执行
                    task = self._process_api_error_task(client_idx, person_key, movie_title, person_name)
                    current_tasks.append(task)
                
                # 控制并发，每concurrency个并行请求
                for j in range(0, len(current_tasks), concurrency):
                    sub_batch = current_tasks[j:j+concurrency]

                    # 添加调试信息和错误处理
                    if not isinstance(sub_batch, list):
                        logger.error(f"sub_batch 不是列表类型: {type(sub_batch)}, 值: {sub_batch}")
                        continue

                    # 并行执行这一小批次的请求
                    results = await asyncio.gather(*sub_batch, return_exceptions=True)

                    # 处理结果
                    for k, result in enumerate(results):
                        if j+k >= len(batch):  # 防止索引越界
                            continue

                        person_key = batch[j+k][0]

                        if isinstance(result, Exception):
                            safe_error = self._safe_encode_string(str(result))
                            safe_key = self._safe_encode_string(person_key)
                            logger.error(f"API错误重试异常: {safe_key} - {safe_error}")
                            failed_retries += 1
                            continue

                        success = result
                        if success:
                            successful_retries += 1

                            # 更新错误重试状态
                            if person_key in api_errors:
                                api_errors[person_key]['retry_success'] = True
                                api_errors[person_key]['retry_time'] = datetime.now().isoformat()
                        else:
                            failed_retries += 1

                            # 更新重试次数
                            if person_key in api_errors:
                                api_errors[person_key]['retry_count'] = api_errors[person_key].get('retry_count', 0) + 1
                                api_errors[person_key]['last_retry'] = datetime.now().isoformat()

                    # 更新进度条 - 添加错误处理
                    try:
                        batch_size_update = len(sub_batch)
                        if not isinstance(batch_size_update, int):
                            logger.error(f"len(sub_batch) 返回非整数: {type(batch_size_update)}, 值: {batch_size_update}")
                            batch_size_update = 1  # 使用默认值
                        pbar.update(batch_size_update)
                    except Exception as e:
                        logger.error(f"更新进度条时出错: {str(e)}, sub_batch类型: {type(sub_batch)}, 值: {sub_batch}")
                        pbar.update(1)  # 使用默认值继续
                    safe_success = self._safe_encode_string(str(successful_retries))
                    safe_failed = self._safe_encode_string(str(failed_retries))
                    safe_total = self._safe_encode_string(f"{successful_retries+failed_retries}/{len(tasks)}")
                    pbar.set_postfix({
                        '成功': safe_success,
                        '失败': safe_failed,
                        '总计': safe_total
                    })
                
                # 每批次后保存进度
                if (successful_retries + failed_retries) % batch_size == 0:
                    self._save_results()
        
        safe_client_idx = self._safe_encode_string(str(client_idx+1))
        safe_success = self._safe_encode_string(str(successful_retries))
        safe_failed = self._safe_encode_string(str(failed_retries))
        logger.info(f"API密钥 {safe_client_idx}/{len(self.clients)} 完成API错误重试，成功: {safe_success}, 失败: {safe_failed}")
        return successful_retries, failed_retries
    
    async def _process_api_error_task(self, client_idx: int, person_key: str, movie_title: str, person_name: str) -> bool:
        """处理单个API错误重试任务
        
        Returns:
            布尔值，指示重试是否成功
        """
        try:
            api_response = await self._query_infini_api(movie_title, person_name, client_idx)
            
            if 'error' not in api_response:
                logger.info(f"API错误重试成功: {person_key}")
                
                # 尝试解析person_key，提取movie_id
                movie_id = None
                try:
                    parts = person_key.split('_', 1)
                    if len(parts) == 2:
                        movie_id = int(parts[0])
                except Exception:
                    # 如果无法从person_key提取movie_id，则尝试在数据集中查找
                    title_field = self.get_title_field()
                    for idx, row in self.df.iterrows():
                        if title_field in row and row[title_field] == movie_title:
                            movie_id = idx
                            break
                
                # 确定结果键
                result_key = person_key
                if movie_id is not None and '_' not in person_key:
                    result_key = f"{movie_id}_{person_name}"
                
                # 更新结果
                self.results[result_key] = api_response
                return True
            else:
                logger.warning(f"API错误重试失败: {person_key} - {api_response['error']}")
                return False
                
        except Exception as e:
            logger.error(f"处理API错误重试任务时出错: {str(e)}")
            return False

    def export_current_processing_results(self) -> None:
        """导出当前的处理结果到pkl和csv文件"""
        if not self.results:
            logger.warning("没有可导出的结果")
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        export_data = []
        
        for person_key, result in self.results.items():
            if 'error' in result and 'extracted_data' not in result:
                continue
                
            movie_id, person_name = person_key.split('_', 1)
            movie_title = ""
            if int(movie_id) in self.df.index:
                title_field = self.get_title_field()
                movie_title = self.df.loc[int(movie_id)][title_field]
                
            row = {
                'movie_id': movie_id,
                'movie_title': movie_title,
                'person_name': person_name,
                'role': self.field_to_process,
                'request_time': result.get('request_time'),
                'response_timestamp': result.get('response_timestamp'),
                'response_time_seconds': result.get('response_time_seconds'),
                'model': result.get('model'),
                'temperature': result.get('temperature'),
                'http_status_code': result.get('http_status_code')
            }
            
            if 'extracted_data' in result and result['extracted_data']:
                extracted_data = result['extracted_data']
                if isinstance(extracted_data, dict):
                    for key, value in extracted_data.items():
                        row[key] = value
                elif isinstance(extracted_data, list):
                    row['extracted_data'] = json.dumps(extracted_data, ensure_ascii=False, default=str)
                else:
                    row['extracted_data'] = str(extracted_data)
                    
            if 'raw_response' in result:
                row['raw_response_json'] = json.dumps(result['raw_response'], ensure_ascii=False, default=str)
            if 'message_content' in result:
                row['message_content'] = result['message_content']
            if 'error' in result:
                row['error'] = result['error']
                
            export_data.append(row)
            
        export_df = pd.DataFrame(export_data)
        
        # 导出为pkl文件
        pkl_output_path = self.output_dir / f"infini_{self.field_to_process}_results_{timestamp}.pkl"
        export_df.to_pickle(pkl_output_path)
        logger.info(f"当前结果已导出到pickle文件: {pkl_output_path}")
        
        # 导出为csv文件
        csv_output_path = self.output_dir / f"infini_{self.field_to_process}_results_{timestamp}.csv"
        export_df.to_csv(csv_output_path, index=False, encoding='utf-8-sig')
        logger.info(f"当前结果已导出到CSV文件: {csv_output_path}")

    def update_checkpoint_from_results(self):
        """根据已有结果更新检查点进度 - 修复版本"""
        if not self.results:
            return

        # 注意：不应该根据部分处理的结果来更新整体进度
        # 因为一个电影可能有多个编剧，只处理了其中一部分
        # 这里只更新已处理的ID列表，但不更新last_processed_index

        # 从结果中提取所有已处理的电影ID（仅用于记录）
        processed_movie_ids = set()
        for person_key in self.results.keys():
            try:
                movie_id = int(person_key.split('_', 1)[0])
                processed_movie_ids.add(movie_id)
            except (ValueError, IndexError):
                continue

        # 更新已处理ID列表（仅用于记录，不影响进度计算）
        self.checkpoint["processed_ids"] = list(set(self.checkpoint["processed_ids"]) | processed_movie_ids)

        # 重要：不更新last_processed_index，因为：
        # 1. 一个电影可能有多个编剧，只处理了部分编剧不代表整个电影处理完成
        # 2. 应该通过实际的筛选逻辑来确定哪些记录需要处理
        # 3. last_processed_index应该只在实际处理完成后更新

        logger.info(f"从结果文件中识别出 {len(processed_movie_ids)} 个电影有部分编剧已处理")
        logger.info(f"但不更新last_processed_index，将通过筛选逻辑确定实际需要处理的记录")

        # 保存更新后的检查点
        self._save_checkpoint()

    def pause_processing(self):
        """暂停处理"""
        self.paused = True
        logger.info("处理已暂停")

    def resume_processing(self):
        """继续处理"""
        self.paused = False
        logger.info("处理已继续")

    def stop_processing(self):
        """停止处理"""
        self.should_stop = True
        logger.info("处理已停止")

    def get_processing_status(self):
        """获取处理状态"""
        return {
            'paused': self.paused,
            'should_stop': self.should_stop,
            'processed_count': getattr(self, 'processed_count', 0),
            'total_count': getattr(self, 'total_count', 0),
            'current_item': getattr(self, 'current_item', ''),
            'success_rate': getattr(self, 'success_rate', 0),
            'error_count': getattr(self, 'error_count', 0)
        }



def load_config(config_path=CONFIG_FILE):
    config_path = Path(config_path)
    if not config_path.exists():
        logger.error(f"Configuration file not found: {config_path}")
        default_config = {
            "api_keys": ["YOUR_INFINI_API_KEY_HERE"],
            "max_workers": 30,
            "batch_size": 100,
            "output_dir": "infini_output",
            "retry_refused": True,
            "retry_failed": True,
            "field_to_process": "director",
            "data_type": "movie",
            "data_type_mappings": {
                "movie": {
                    "title_field": "biaoti",
                    "fields": {
                        "director": "daoyan",
                        "screenwriter": "bianjv",
                        "actor": "zhuyan"
                    }
                },
                "book": {
                    "title_field": "title",
                    "fields": {
                        "author": "author",
                        "translator": "translator",
                        "editor": "editor"
                    }
                },
                "tv": {
                    "title_field": "name",
                    "fields": {
                        "director": "director",
                        "screenwriter": "writer",
                        "actor": "cast"
                    }
                }
            }
        }
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=4, default=str)
        logger.info(f"Created template configuration file: {config_path}")
        logger.info(f"Please edit the file and set your API keys before running again.")
        sys.exit(1)
        
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        if "api_keys" not in config or not config["api_keys"] or config["api_keys"][0] == "YOUR_INFINI_API_KEY_HERE":
            logger.error(f"API keys not set in {config_path}")
            logger.info(f"Please edit {config_path} and set your API keys.")
            sys.exit(1)

        # 确保数据类型映射配置存在，如果不存在则添加默认配置
        if "data_type_mappings" not in config:
            logger.info("Adding default data type mappings to configuration")
            config["data_type_mappings"] = {
                "movie": {
                    "title_field": "biaoti",
                    "fields": {
                        "director": "daoyan",
                        "screenwriter": "bianjv",
                        "actor": "zhuyan"
                    }
                },
                "book": {
                    "title_field": "title",
                    "fields": {
                        "author": "author",
                        "translator": "translator",
                        "editor": "editor"
                    }
                },
                "tv": {
                    "title_field": "name",
                    "fields": {
                        "director": "director",
                        "screenwriter": "writer",
                        "actor": "cast"
                    }
                }
            }

        # 确保默认数据类型存在
        if "data_type" not in config:
            config["data_type"] = "movie"

        return config
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing configuration file: {str(e)}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Error loading configuration: {str(e)}")
        sys.exit(1)

async def main():
    parser = argparse.ArgumentParser(description='Process Douban movie data with Infini-AI API')
    parser.add_argument('--config', default=CONFIG_FILE, help='Path to configuration file')
    parser.add_argument('--data-path', help='Path to data (CSV files directory, single CSV, or pickle file)')
    parser.add_argument('--field', choices=['director', 'screenwriter', 'actor'], default=None, help='Field to process')
    parser.add_argument('--extract-errors', action='store_true', help='Extract API errors from results')
    parser.add_argument('--retry-errors', action='store_true', help='Retry API errors')
    parser.add_argument('--error-file', help='Specific API error file to retry')
    parser.add_argument('--retry-refused', action='store_true', help='Retry refused requests')
    parser.add_argument('--retry-failed', action='store_true', help='Retry failed requests')
    parser.add_argument('--min-reviews', type=int, help='Minimum number of reviews (pingjiaShu) required')
    parser.add_argument('--min-short-reviews', type=int, help='Minimum number of short reviews (duanpingShu) required')
    args = parser.parse_args()
    
    # 创建全局_safe_encode_string函数用于安全日志记录
    def safe_encode_string(text):
        if not isinstance(text, str):
            text = str(text)
            
        try:
            # 对特殊字符进行处理，使其能在任何编码环境中安全地显示
            return text.encode('utf-8', errors='backslashreplace').decode('utf-8')
        except Exception:
            # 如果出现任何错误，返回简单的占位符
            return "[无法显示的字符]"
    
    try:
        config = load_config(args.config)
        data_path = args.data_path
        
        if not data_path:
            for path in ["dataset/all_data_cleaned.pkl", "dataset/all_data.pkl", "dataset"]:
                if os.path.exists(path):
                    data_path = path
                    break
            if not data_path:
                logger.error("Data path not specified and could not be auto-detected.")
                sys.exit(1)
        
        # 正确处理参数，避免0值被忽略
        if args.min_reviews is not None:
            min_reviews = args.min_reviews
        else:
            min_reviews = config.get("min_reviews", 1)
            
        if args.min_short_reviews is not None:
            min_short_reviews = args.min_short_reviews
        else:
            min_short_reviews = config.get("min_short_reviews", 0)
            
        retry_refused = args.retry_refused or config.get("retry_refused", True)
        retry_failed = args.retry_failed or config.get("retry_failed", True)
        
        logger.info(f"使用参数: min_reviews={min_reviews}, min_short_reviews={min_short_reviews}")
                
        # 从配置文件中获取field参数，如果命令行没有指定
        field_to_process = args.field if args.field else config.get("field_to_process", "director")

        processor = InfiniDirectorProcessor(
            api_keys=config["api_keys"],
            data_path=data_path,
            output_dir=config.get("output_dir", "infini_output"),
            max_workers=config.get("max_workers", 30),
            batch_size=config.get("batch_size", 100),
            field_to_process=field_to_process,  # 使用变量而不是直接使用args.field
            test_mode=config.get("test_mode", False),
            test_limit=config.get("test_limit", 50),
            min_reviews=min_reviews,
            min_short_reviews=min_short_reviews,
            retry_refused=retry_refused,
            retry_failed=retry_failed,
            config=config
        )
        
        processor.load_data()
        
        if args.extract_errors:
            logger.info("提取API错误模式...")
            api_errors = processor.extract_api_errors()
            safe_count = safe_encode_string(str(len(api_errors)))
            logger.info(f"提取完成，共找到 {safe_count} 个错误")
            
        elif args.retry_errors:
            logger.info("重试API错误模式...")
            result = await processor.retry_api_errors(args.error_file)
            safe_success = safe_encode_string(str(result['success']))
            safe_failed = safe_encode_string(str(result['failed']))
            safe_remaining = safe_encode_string(str(result['remaining']))
            logger.info(f"重试完成：成功 {safe_success}，失败 {safe_failed}，剩余 {safe_remaining}")
            
        else:
            # 正常处理流程
            await processor.process_data()
            results_path = processor.export_results(export_format='csv')
            safe_path = safe_encode_string(results_path)
            logger.info(f"Results exported to: {safe_path}")
            errors_path = processor.export_errors()
            if errors_path:
                safe_error_path = safe_encode_string(errors_path)
                logger.info(f"Errors exported to: {safe_error_path}")
                
    except KeyboardInterrupt:
        logger.info("Processing interrupted by user. Progress saved to checkpoint.")
    except Exception as e:
        safe_error = safe_encode_string(str(e))
        logger.error(f"Error processing data: {safe_error}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(main())