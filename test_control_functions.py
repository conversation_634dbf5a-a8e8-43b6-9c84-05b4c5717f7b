#!/usr/bin/env python3
"""
测试任务控制功能的脚本
"""

import requests
import time
import json

BASE_URL = "http://127.0.0.1:5000"

def test_task_control():
    """测试任务控制功能"""
    
    print("=== 测试任务控制功能 ===")
    
    # 1. 启动一个新任务
    print("\n1. 启动新任务...")
    start_data = {
        "filename": "test_control_large.csv",
        "field_to_process": "director",
        "concurrency": 1,
        "min_reviews": 0,
        "min_short_reviews": 0,
        "data_type": "movie"
    }
    
    response = requests.post(f"{BASE_URL}/api/start_processing", json=start_data)
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            task_id = result.get('task_id')
            print(f"✓ 任务启动成功，ID: {task_id}")
        else:
            print(f"✗ 任务启动失败: {result.get('message')}")
            return
    else:
        print(f"✗ 请求失败: {response.status_code}")
        return
    
    # 2. 等待任务开始运行
    print("\n2. 等待任务开始运行...")
    time.sleep(3)
    
    # 3. 获取任务状态
    print("\n3. 获取任务状态...")
    response = requests.get(f"{BASE_URL}/api/task_status/{task_id}")
    if response.status_code == 200:
        status = response.json()
        print(f"✓ 任务状态: {status.get('status')}")
        print(f"  进度信息: {status.get('progress', {})}")
    else:
        print(f"✗ 获取状态失败: {response.status_code}")
    
    # 4. 测试暂停功能
    print("\n4. 测试暂停功能...")
    response = requests.post(f"{BASE_URL}/api/pause_processing/{task_id}")
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print("✓ 任务暂停成功")
        else:
            print(f"✗ 任务暂停失败: {result.get('message')}")
    else:
        print(f"✗ 暂停请求失败: {response.status_code}")
    
    # 5. 检查暂停状态
    time.sleep(1)
    response = requests.get(f"{BASE_URL}/api/task_status/{task_id}")
    if response.status_code == 200:
        status = response.json()
        print(f"  暂停后状态: {status.get('status')}")
        print(f"  是否暂停: {status.get('progress', {}).get('is_paused', False)}")
    
    # 6. 测试继续功能
    print("\n5. 测试继续功能...")
    response = requests.post(f"{BASE_URL}/api/resume_processing/{task_id}")
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print("✓ 任务继续成功")
        else:
            print(f"✗ 任务继续失败: {result.get('message')}")
    else:
        print(f"✗ 继续请求失败: {response.status_code}")
    
    # 7. 检查继续状态
    time.sleep(1)
    response = requests.get(f"{BASE_URL}/api/task_status/{task_id}")
    if response.status_code == 200:
        status = response.json()
        print(f"  继续后状态: {status.get('status')}")
        print(f"  是否暂停: {status.get('progress', {}).get('is_paused', False)}")
    
    # 8. 测试停止功能
    print("\n6. 测试停止功能...")
    response = requests.post(f"{BASE_URL}/api/stop_processing/{task_id}")
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print("✓ 任务停止成功")
        else:
            print(f"✗ 任务停止失败: {result.get('message')}")
    else:
        print(f"✗ 停止请求失败: {response.status_code}")
    
    # 9. 检查停止状态
    time.sleep(1)
    response = requests.get(f"{BASE_URL}/api/task_status/{task_id}")
    if response.status_code == 200:
        status = response.json()
        print(f"  停止后状态: {status.get('status')}")
        print(f"  应该停止: {status.get('progress', {}).get('should_stop', False)}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_task_control()
