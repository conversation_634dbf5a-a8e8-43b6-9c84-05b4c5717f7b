{% extends "base.html" %}

{% block title %}任务管理 - 豆瓣电影数据处理工具{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2>
                    <i class="fas fa-tasks me-2"></i> 任务管理
                </h2>
                <p class="text-muted">管理和监控所有数据处理任务</p>
            </div>
            <div>
                <a href="{{ url_for('upload_file') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> 新建任务
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 任务统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card border-primary">
            <div class="card-body text-center">
                <div class="display-6 text-primary" id="runningTasksCount">0</div>
                <div class="text-muted">运行中任务</div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card border-warning">
            <div class="card-body text-center">
                <div class="display-6 text-warning" id="pausedTasksCount">0</div>
                <div class="text-muted">暂停任务</div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card border-success">
            <div class="card-body text-center">
                <div class="display-6 text-success" id="completedTasksCount">0</div>
                <div class="text-muted">已完成任务</div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card border-danger">
            <div class="card-body text-center">
                <div class="display-6 text-danger" id="errorTasksCount">0</div>
                <div class="text-muted">错误任务</div>
            </div>
        </div>
    </div>
</div>

<!-- 任务列表 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list"></i> 任务列表
                </h5>
                <button class="btn btn-outline-primary btn-sm" onclick="refreshTasks()">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
            </div>
            <div class="card-body">
                <div id="tasksContainer">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <div class="mt-2">正在加载任务列表...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 任务详情模态框 -->
<div class="modal fade" id="taskDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle"></i> 任务详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="taskDetailContent">
                <!-- 任务详情内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<script>
let refreshInterval;

$(document).ready(function() {
    loadTasks();
    
    // 每5秒自动刷新
    refreshInterval = setInterval(loadTasks, 5000);
});

function loadTasks() {
    $.get('/api/tasks', function(data) {
        if (data.success) {
            displayTasks(data.tasks);
            updateStatistics(data.tasks);
        } else {
            showError('加载任务失败: ' + data.message);
        }
    }).fail(function() {
        showError('网络错误，无法加载任务列表');
    });
}

function displayTasks(tasks) {
    const container = $('#tasksContainer');
    
    if (tasks.length === 0) {
        container.html(`
            <div class="text-center py-4">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无任务</h5>
                <p class="text-muted">点击"新建任务"开始处理数据</p>
                <a href="${window.location.origin}/upload" class="btn btn-primary">
                    <i class="fas fa-plus"></i> 新建任务
                </a>
            </div>
        `);
        return;
    }
    
    let html = '<div class="table-responsive"><table class="table table-hover">';
    html += `
        <thead class="table-light">
            <tr>
                <th>任务ID</th>
                <th>文件名</th>
                <th>处理字段</th>
                <th>状态</th>
                <th>进度</th>
                <th>开始时间</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
    `;
    
    tasks.forEach(function(task) {
        const statusInfo = getStatusInfo(task.status);
        const progress = task.progress || 0;
        const progressData = task.progress_data || {};
        
        html += `
            <tr>
                <td><code>${task.task_id.substring(0, 12)}...</code></td>
                <td>${task.filename}</td>
                <td><span class="badge bg-info">${task.field_to_process}</span></td>
                <td>
                    <span class="badge bg-${statusInfo.color}">
                        <i class="fas ${statusInfo.icon}"></i> ${statusInfo.text}
                    </span>
                </td>
                <td>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar bg-${statusInfo.color}" 
                             style="width: ${progress}%"
                             aria-valuenow="${progress}" 
                             aria-valuemin="0" 
                             aria-valuemax="100">
                            ${progress.toFixed(1)}%
                        </div>
                    </div>
                    ${progressData.processed_count ? 
                        `<small class="text-muted">${progressData.processed_count}/${progressData.total_count}</small>` : 
                        ''}
                </td>
                <td>${new Date(task.start_time).toLocaleString()}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        ${getActionButtons(task)}
                        <button class="btn btn-outline-info" onclick="viewTaskDetail('${task.task_id}')" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    container.html(html);
}

function getStatusInfo(status) {
    switch(status) {
        case 'running':
            return { color: 'primary', icon: 'fa-play', text: '运行中' };
        case 'paused':
            return { color: 'warning', icon: 'fa-pause', text: '已暂停' };
        case 'stopped':
            return { color: 'danger', icon: 'fa-stop', text: '已停止' };
        case 'completed':
            return { color: 'success', icon: 'fa-check', text: '已完成' };
        case 'error':
            return { color: 'danger', icon: 'fa-exclamation-triangle', text: '错误' };
        default:
            return { color: 'secondary', icon: 'fa-question', text: '未知' };
    }
}

function getActionButtons(task) {
    let buttons = '';
    
    if (task.status === 'running') {
        buttons += `
            <button class="btn btn-outline-warning" onclick="pauseTask('${task.task_id}')" title="暂停">
                <i class="fas fa-pause"></i>
            </button>
            <button class="btn btn-outline-danger" onclick="stopTask('${task.task_id}')" title="停止">
                <i class="fas fa-stop"></i>
            </button>
        `;
    } else if (task.status === 'paused') {
        buttons += `
            <button class="btn btn-outline-success" onclick="resumeTask('${task.task_id}')" title="继续">
                <i class="fas fa-play"></i>
            </button>
            <button class="btn btn-outline-danger" onclick="stopTask('${task.task_id}')" title="停止">
                <i class="fas fa-stop"></i>
            </button>
        `;
    }
    
    return buttons;
}

function updateStatistics(tasks) {
    const stats = {
        running: 0,
        paused: 0,
        completed: 0,
        error: 0
    };
    
    tasks.forEach(task => {
        if (task.status === 'running') stats.running++;
        else if (task.status === 'paused') stats.paused++;
        else if (task.status === 'completed') stats.completed++;
        else if (task.status === 'error') stats.error++;
    });
    
    $('#runningTasksCount').text(stats.running);
    $('#pausedTasksCount').text(stats.paused);
    $('#completedTasksCount').text(stats.completed);
    $('#errorTasksCount').text(stats.error);
}

function pauseTask(taskId) {
    $.post(`/api/pause_processing/${taskId}`, function(data) {
        if (data.success) {
            showSuccess('任务已暂停');
            loadTasks();
        } else {
            showError('暂停失败: ' + data.message);
        }
    });
}

function resumeTask(taskId) {
    $.post(`/api/resume_processing/${taskId}`, function(data) {
        if (data.success) {
            showSuccess('任务已继续');
            loadTasks();
        } else {
            showError('继续失败: ' + data.message);
        }
    });
}

function stopTask(taskId) {
    if (confirm('确定要停止这个任务吗？停止后将无法继续。')) {
        $.post(`/api/stop_processing/${taskId}`, function(data) {
            if (data.success) {
                showSuccess('任务已停止');
                loadTasks();
            } else {
                showError('停止失败: ' + data.message);
            }
        });
    }
}

function viewTaskDetail(taskId) {
    $.get(`/api/task_status/${taskId}`, function(data) {
        if (data.success) {
            const task = data;
            const progress = task.progress || {};
            
            const html = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>基本信息</h6>
                        <table class="table table-sm">
                            <tr><td>任务ID</td><td><code>${task.task_id}</code></td></tr>
                            <tr><td>文件名</td><td>${task.filename}</td></tr>
                            <tr><td>处理字段</td><td>${task.field_to_process}</td></tr>
                            <tr><td>状态</td><td><span class="badge bg-primary">${task.status}</span></td></tr>
                            <tr><td>开始时间</td><td>${new Date(task.start_time).toLocaleString()}</td></tr>
                            ${task.end_time ? `<tr><td>结束时间</td><td>${new Date(task.end_time).toLocaleString()}</td></tr>` : ''}
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>处理进度</h6>
                        <table class="table table-sm">
                            <tr><td>已处理</td><td>${progress.processed_count || 0}</td></tr>
                            <tr><td>总数量</td><td>${progress.total_count || 0}</td></tr>
                            <tr><td>成功率</td><td>${progress.success_rate ? progress.success_rate.toFixed(1) + '%' : '-'}</td></tr>
                            <tr><td>错误数</td><td>${progress.error_count || 0}</td></tr>
                            <tr><td>当前项目</td><td>${progress.current_item || '-'}</td></tr>
                        </table>
                    </div>
                </div>
            `;
            
            $('#taskDetailContent').html(html);
            $('#taskDetailModal').modal('show');
        } else {
            showError('获取任务详情失败: ' + data.message);
        }
    });
}

function refreshTasks() {
    loadTasks();
    showSuccess('任务列表已刷新');
}

function showSuccess(message) {
    showAlert('success', message);
}

function showError(message) {
    showAlert('danger', message);
}

function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container-fluid').first().prepend(alertHtml);
    
    // 3秒后自动消失
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 3000);
}
</script>
{% endblock %}
